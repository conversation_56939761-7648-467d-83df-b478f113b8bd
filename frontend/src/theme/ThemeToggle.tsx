import React, { useState } from "react";
import {
  I<PERSON><PERSON><PERSON>on,
  Toolt<PERSON>,
  useTheme as useMuiTheme,
  Menu,
  MenuItem,
  Typography,
  Box,
} from "@mui/material";
import {
  Brightness4 as DarkModeIcon,
  Brightness7 as LightModeIcon,
  Settings as SettingsIcon,
  ChevronRight as ChevronRightIcon,
  Language as LanguageIcon,
} from "@mui/icons-material";
import { useTheme } from "./ThemeContext";

interface ThemeToggleProps {
  size?: "small" | "medium" | "large";
  tooltip?: boolean;
}

export const ThemeToggle: React.FC<ThemeToggleProps> = ({
  size = "medium",
  tooltip = true,
}) => {
  const { toggleTheme } = useTheme();
  const theme = useMuiTheme();
  const isDarkMode = theme.palette.mode === "dark";
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleThemeClick = () => {
    toggleTheme();
    handleClose();
  };

  const button = (
    <IconButton
      onClick={handleClick}
      size={size}
      aria-label="Settings"
      aria-controls={open ? "settings-menu" : undefined}
      aria-haspopup="true"
      aria-expanded={open ? "true" : undefined}
    >
      <SettingsIcon fontSize={size === "small" ? "small" : "medium"} />
    </IconButton>
  );

  return (
    <>
      {tooltip ? <Tooltip title="Settings">{button}</Tooltip> : button}

      <Menu
        id="settings-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: 220,
            maxWidth: "100%",
          },
        }}
      >
        <MenuItem
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            <LanguageIcon sx={{ mr: 1 }} />
            <Typography variant="body2">Language: English</Typography>
          </Box>
          <ChevronRightIcon />
        </MenuItem>
        <MenuItem
          onClick={handleThemeClick}
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box sx={{ display: "flex", alignItems: "center" }}>
            {isDarkMode ? (
              <LightModeIcon sx={{ mr: 1 }} />
            ) : (
              <DarkModeIcon sx={{ mr: 1 }} />
            )}
            <Typography variant="body2">Theme</Typography>
          </Box>
          <ChevronRightIcon />
        </MenuItem>
      </Menu>
    </>
  );
};
