"use client";
import { Box, Tooltip, alpha } from "@mui/material";
import { observer } from "mobx-react";
import React, {
  useMemo,
  useEffect,
  useRef,
  useCallback,
  useState,
  useContext,
} from "react";
import { StoreContext } from "../../store";
import "./styles.css";
import {
  formatTime,
  normalizeTimeFrame,
  calculateTimelinePosition,
  getTimelineContainerWidth,
} from "../../utils/timeUtils";
import { TextFields, Image, VideoFile, AudioFile } from "@mui/icons-material";
import {
  useVideoThumbnail,
  useAudioWaveform,
} from "../../hooks/timelineMediaHooks";
import { useElementDrag } from "./hooks/useElementDrag";
import { SnapIndicator } from "./SnapIndicator";
import { useDraggable } from "@dnd-kit/core";
import { useTrackDnd } from "./TrackDndContext";
import { FixedSizeList as List } from "react-window";
import AutoSizer from "react-virtualized-auto-sizer";

// 常量和配置
// =================================

// 时间线高度常量
const TIMELINE_HEIGHT = "36px";
const ITEM_HEIGHT = 36;
const OVERSCAN_COUNT = 3; // 减少过扫描数量
const MIN_ELEMENT_WIDTH = 40;
const DEFAULT_HANDLE_WIDTH = 8;
const MIN_HANDLE_WIDTH = 2;

// 元素类型到图标/颜色的映射
const ELEMENT_COLORS = {
  text: "#ff9800",
  image: "#4caf50",
  video: "#f44336",
  audio: "#3f51b5",
  default: "#9e9e9e",
} as const;

// 元素内容渲染映射 - 使用函数而不是对象，避免每次重新创建
const getElementContent = (element: any) => {
  switch (element.type) {
    case "text":
      return {
        icon: <TextFields fontSize="small" sx={{ mr: 0.5 }} />,
        text: element.properties?.text ?? element.name,
      };
    case "image":
      return {
        icon: <Image fontSize="small" sx={{ mr: 0.5 }} />,
        text: element.name,
      };
    case "video":
      return {
        icon: <VideoFile fontSize="small" sx={{ mr: 0.5 }} />,
        text: element.name,
      };
    case "audio":
      return {
        icon: <AudioFile fontSize="small" sx={{ mr: 0.5 }} />,
        text: element.name,
      };
    default:
      return { icon: null, text: element.name || "" };
  }
};

// 组件类型定义
interface ElementContentProps {
  element: any;
}

interface DurationLabelProps {
  duration: string;
  isVisible: boolean;
}

interface TimeFrameViewProps {
  element: any;
  containerWidth?: number | null;
  handleTimeFrameChange?: (element: any, start: number, end: number) => void;
  allElements?: any[];
  isDraggable?: boolean;
}

// 优化的元素内容组件
const ElementContent = React.memo(
  ({ element }: ElementContentProps) => {
    const [showText, setShowText] = useState(true);
    const boxRef = useRef<HTMLDivElement>(null);

    // 使用 useMemo 缓存内容
    const content = useMemo(
      () => getElementContent(element),
      [element.type, element.name, element.properties?.text]
    );

    // 优化 ResizeObserver 回调
    const handleResize = useCallback((entries: ResizeObserverEntry[]) => {
      const entry = entries[0];
      if (entry) {
        setShowText(entry.contentRect.width > 50);
      }
    }, []);

    useEffect(() => {
      const element = boxRef.current;
      if (!element) return;

      const observer = new ResizeObserver(handleResize);
      observer.observe(element);
      return () => observer.disconnect();
    }, [handleResize]);

    // 静态样式对象，避免重复创建
    const boxStyle = useMemo(
      () => ({
        display: "flex",
        alignItems: "center",
        width: "100%",
        p: 0.2,
        bgcolor: "rgba(0,0,0,0.2)",
        borderRadius: 1,
        minWidth: 0,
        overflow: "hidden",
      }),
      []
    );

    const textStyle = useMemo(
      () => ({
        overflow: "hidden",
        textOverflow: "ellipsis",
        whiteSpace: "nowrap" as const,
        width: "100%",
        userSelect: "none" as const,
        minWidth: 0,
      }),
      []
    );

    return (
      <Box ref={boxRef} sx={boxStyle}>
        {content.icon}
        {showText && <span style={textStyle}>{content.text}</span>}
      </Box>
    );
  },
  (prevProps, nextProps) => {
    const prev = prevProps.element;
    const next = nextProps.element;
    return (
      prev.id === next.id &&
      prev.type === next.type &&
      prev.name === next.name &&
      prev.properties?.text === next.properties?.text
    );
  }
);

// 优化的持续时间标签组件
const DurationLabel = React.memo(
  ({ duration, isVisible }: DurationLabelProps) => {
    const [displayState, setDisplayState] = useState({
      showLabel: true,
      compactMode: false,
    });
    const labelRef = useRef<HTMLDivElement>(null);

    const handleResize = useCallback((entries: ResizeObserverEntry[]) => {
      const entry = entries[0];
      if (!entry) return;

      const parentWidth = entry.contentRect.width;
      setDisplayState({
        showLabel: parentWidth >= 70,
        compactMode: parentWidth < 100,
      });
    }, []);

    useEffect(() => {
      const element = labelRef.current?.parentElement;
      if (!element) return;

      const observer = new ResizeObserver(handleResize);
      observer.observe(element);
      return () => observer.disconnect();
    }, [handleResize]);

    const labelStyle = useMemo(
      () => ({
        position: "absolute" as const,
        right: displayState.compactMode ? 2 : 4,
        top: "50%",
        transform: "translateY(-50%)",
        fontSize: displayState.compactMode ? "0.7rem" : "0.8rem",
        opacity: isVisible ? 1 : 0,
        transition: "opacity 0.2s",
        pointerEvents: "none" as const,
        userSelect: "none" as const,
      }),
      [displayState.compactMode, isVisible]
    );

    if (!displayState.showLabel) return null;

    return (
      <div ref={labelRef} style={labelStyle}>
        {duration}
      </div>
    );
  }
);

// 优化的主时间帧视图组件
export const TimeFrameView = observer(
  ({
    element,
    containerWidth = null,
    handleTimeFrameChange,
    allElements = [],
    isDraggable = false,
  }: TimeFrameViewProps) => {
    const store = useContext(StoreContext);
    const isSelected = store.selectedElement?.id === element.id;
    const [isDragging, setIsDragging] = useState(false);
    const [isHovering, setIsHovering] = useState(false);
    const [elementWidth, setElementWidth] = useState(0);

    const elementRef = useRef<HTMLDivElement>(null);
    const videoThumbnail = useVideoThumbnail(element);
    const audioWaveform = useAudioWaveform(element);

    // 使用拖拽上下文
    const { activeElement } = useTrackDnd();
    const isBeingDragged = activeElement?.id === element.id;

    // 配置垂直拖拽功能
    const {
      attributes,
      listeners,
      setNodeRef,
      transform,
      isDragging: isDndDragging,
    } = useDraggable({
      id: element.id,
      data: {
        type: "element",
        element: element,
      },
      disabled: !isDraggable,
    });

    // 使用元素拖拽钩子
    const {
      handleLeftHandleDrag,
      handleRightHandleDrag,
      handleCenterDrag,
      isSnappedStart,
      isSnappedEnd,
    } = useElementDrag({
      element,
      allElements,
      containerWidth: containerWidth || getTimelineContainerWidth(),
      store,
    });

    // 监测元素宽度变化
    useEffect(() => {
      const element = elementRef.current;
      if (!element) return;

      const observer = new ResizeObserver((entries) => {
        const entry = entries[0];
        if (entry) {
          setElementWidth(entry.contentRect.width);
        }
      });

      observer.observe(element);
      return () => observer.disconnect();
    }, []);

    // 计算缓存值
    const elementColor = useMemo(
      () =>
        ELEMENT_COLORS[element.type as keyof typeof ELEMENT_COLORS] ||
        ELEMENT_COLORS.default,
      [element.type]
    );

    const duration = useMemo(
      () => formatTime(element.timeFrame.end - element.timeFrame.start),
      [element.timeFrame.start, element.timeFrame.end]
    );

    const handleWidth = useMemo(
      () =>
        elementWidth < MIN_ELEMENT_WIDTH
          ? MIN_HANDLE_WIDTH
          : DEFAULT_HANDLE_WIDTH,
      [elementWidth]
    );

    // 位置样式计算
    const positionStyles = useMemo(() => {
      const currentContainerWidth =
        containerWidth || getTimelineContainerWidth();

      if (!currentContainerWidth) {
        return {
          width: "0%",
          left: "0%",
          transform: "translateZ(0)",
        };
      }

      const width =
        ((element.timeFrame.end - element.timeFrame.start) /
          store.timelineDisplayDuration) *
        100;
      const left = calculateTimelinePosition(
        element.timeFrame.start,
        store.timelineDisplayDuration,
        store.timelinePan.offsetX,
        currentContainerWidth
      );

      return {
        width: `${width}%`,
        left: `${left}%`,
        transform: "translateZ(0)",
        willChange: "transform",
      };
    }, [
      element.timeFrame.start,
      element.timeFrame.end,
      store.timelineDisplayDuration,
      store.timelinePan.offsetX,
      containerWidth,
    ]);

    // 控制把手样式
    const handleStyles = useMemo(
      () => ({
        bgcolor: "white",
        border: 2,
        borderColor: elementColor,
        borderRadius: "2px",
        width: `${handleWidth}px`,
        height: "16px",
        cursor: "ew-resize",
        transition: "all 0.2s",
        opacity: isHovering || isSelected ? 1 : 0.7,
        boxShadow: "0 1px 3px rgba(0,0,0,0.12)",
        "&:hover": {
          bgcolor: alpha(elementColor, 0.2),
          boxShadow: "0 2px 5px rgba(0,0,0,0.2)",
        },
      }),
      [elementColor, isHovering, isSelected, handleWidth]
    );

    // 背景样式
    const backgroundStyles = useMemo(() => {
      const baseStyles = {
        position: "relative" as const,
        "&::after": {
          content: '""',
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "transparent",
          zIndex: 1,
        },
      };

      if (element.type === "image" && element.properties?.src) {
        return {
          ...baseStyles,
          backgroundImage: `url(${element.properties.src})`,
          backgroundSize: "30px 100%",
          backgroundRepeat: "repeat-x",
        };
      }

      if (element.type === "video" && videoThumbnail) {
        return {
          ...baseStyles,
          backgroundImage: `url(${videoThumbnail})`,
          backgroundSize: "30px 100%",
          backgroundRepeat: "repeat-x",
        };
      }

      if (element.type === "audio" && audioWaveform) {
        return {
          ...baseStyles,
          backgroundImage: `url(${audioWaveform})`,
          backgroundSize: "100% 100%",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
          imageRendering: "high-quality",
        };
      }

      return {
        bgcolor: isSelected ? elementColor : alpha(elementColor, 0.8),
        "&:hover": {
          bgcolor: elementColor,
          transform: "scale(1.01)",
        },
      };
    }, [
      element.type,
      element.properties?.src,
      videoThumbnail,
      audioWaveform,
      isSelected,
      elementColor,
    ]);

    // 事件处理器
    const handleElementClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        store.setSelectedElement(element);
      },
      [store, element]
    );

    const handleDoubleClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        store.handleSeek(element.timeFrame.start);
      },
      [store, element.timeFrame.start]
    );

    const handleMouseEnter = useCallback(() => setIsHovering(true), []);
    const handleMouseLeave = useCallback(() => setIsHovering(false), []);

    // 样式对象
    const centerBoxStyle = useMemo(
      () => ({
        height: "100%",
        width: "100%",
        color: "white",
        fontSize: "0.75rem",
        minWidth: "0px",
        border: elementWidth < MIN_ELEMENT_WIDTH ? "2px solid" : "none",
        borderColor:
          elementWidth < MIN_ELEMENT_WIDTH
            ? isSelected
              ? "#2196f3"
              : alpha(elementColor, 0.8)
            : "transparent",
        display: "flex",
        alignItems: "center",
        justifyContent: "space-between",
        transition: "all 0.2s",
        cursor: "move",
        pl: elementWidth < MIN_ELEMENT_WIDTH ? 0 : 1,
        pr: elementWidth < MIN_ELEMENT_WIDTH ? 0 : 1,
        borderRadius: "4px",
        overflow: "hidden",
        boxSizing: "border-box",
        ...backgroundStyles,
      }),
      [backgroundStyles, elementWidth, isSelected, elementColor]
    );

    const containerStyle = useMemo(
      () => ({
        position: "absolute" as const,
        width: "auto",
        height: TIMELINE_HEIGHT,
        transition: "all 0.2s ease",
        bgcolor: "transparent",
        zIndex: isSelected ? 20 : isDragging ? 30 : 10,
        boxSizing: "border-box" as const,
        ...(isSelected && {
          border: "2px dashed rgba(33, 150, 243, 0.6)",
          boxShadow: "0 5px 15px rgba(0, 0, 0, 0.2)",
        }),
        ...positionStyles,
      }),
      [isSelected, isDragging, positionStyles]
    );

    const dragStyle = useMemo(
      () =>
        transform
          ? {
              transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
              zIndex: 1000,
              opacity: 0.8,
              boxShadow: "0 5px 15px rgba(0, 0, 0, 0.2)",
              border: "2px dashed rgba(33, 150, 243, 0.6)",
            }
          : undefined,
      [transform]
    );

    // 鼠标按下处理
    const handleMouseDown = useCallback(
      (e: React.MouseEvent) => {
        const target = e.target as HTMLElement;
        const isLeftHandle =
          target.id === "left-handle" || target.closest("#left-handle");
        const isRightHandle =
          target.id === "right-handle" || target.closest("#right-handle");

        if (isLeftHandle || isRightHandle) return;

        const startX = e.clientX;
        const startY = e.clientY;
        let hasMoved = false;
        let isVerticalDrag = false;
        let dragStarted = false;

        const handleMouseMove = (moveEvent: MouseEvent) => {
          const deltaX = Math.abs(moveEvent.clientX - startX);
          const deltaY = Math.abs(moveEvent.clientY - startY);

          if (!hasMoved) {
            if (deltaY > deltaX && deltaY > 8) {
              isVerticalDrag = true;
              hasMoved = true;
              elementRef.current?.classList.add("vertical-dragging");
            } else if (deltaX > 5) {
              hasMoved = true;
              if (!dragStarted) {
                dragStarted = true;
                // 创建一个符合类型的React.MouseEvent
                const reactMouseEvent = e as React.MouseEvent<HTMLDivElement>;
                handleCenterDrag(reactMouseEvent);
              }
            }
          }
        };

        const handleMouseUp = () => {
          if (isVerticalDrag) {
            elementRef.current?.classList.remove("vertical-dragging");
          }

          if (!hasMoved) {
            store.setSelectedElement(element);
          }

          document.removeEventListener("mousemove", handleMouseMove);
          document.removeEventListener("mouseup", handleMouseUp);
        };

        document.addEventListener("mousemove", handleMouseMove);
        document.addEventListener("mouseup", handleMouseUp);
      },
      [store, element, handleCenterDrag]
    );

    const showControls = (isHovering || isSelected) && !isDragging;

    return (
      <Box
        id={`element-${element.id}`}
        ref={(node: HTMLDivElement | null) => {
          elementRef.current = node;
          if (isDraggable && node) {
            setNodeRef(node);
          }
        }}
        onClick={handleElementClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={`timeline-element-container ${
          isBeingDragged ? "element-dragging" : ""
        } ${isSelected ? "element-selected" : ""}`}
        sx={{
          ...containerStyle,
          ...(dragStyle || {}),
        }}
        onMouseDown={handleMouseDown}
        {...(isDraggable ? { ...attributes, ...listeners } : {})}
      >
        {/* 吸附指示器 */}
        <SnapIndicator position="left" isVisible={isSnappedStart} />
        <SnapIndicator position="right" isVisible={isSnappedEnd} />

        {/* 垂直拖拽指示器 */}
        {isDraggable && <Box className="vertical-drag-indicator" />}

        {/* 中间可拖拽区域 */}
        <Box id="center" onDoubleClick={handleDoubleClick} sx={centerBoxStyle}>
          <ElementContent element={element} />
          <DurationLabel
            duration={duration}
            isVisible={isHovering || isSelected}
          />
        </Box>

        {/* 左侧时间控制把手 */}
        {showControls && (
          <Box
            id="left-handle"
            sx={{
              ...handleStyles,
              position: "absolute",
              top: "50%",
              left: elementWidth < MIN_ELEMENT_WIDTH ? -1 : 0,
              transform: "translate(0%, -50%)",
              zIndex: 30,
              borderColor: isSnappedStart ? "#ff9800" : "#2196f3",
              boxShadow: isSnappedStart
                ? "0 0 8px rgba(255,152,0,0.5)"
                : "none",
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              handleLeftHandleDrag(e);
            }}
          />
        )}

        {/* 右侧时间控制把手 */}
        {showControls && (
          <Box
            id="right-handle"
            sx={{
              ...handleStyles,
              position: "absolute",
              top: "50%",
              right: elementWidth < MIN_ELEMENT_WIDTH ? -1 : 0,
              transform: "translate(0%, -50%)",
              zIndex: 30,
              borderColor: isSnappedEnd ? "#ff9800" : "#2196f3",
              boxShadow: isSnappedEnd ? "0 0 8px rgba(255,152,0,0.5)" : "none",
            }}
            onMouseDown={(e) => {
              e.stopPropagation();
              handleRightHandleDrag(e);
            }}
          />
        )}
      </Box>
    );
  }
);

// 优化的虚拟化列表项组件
const TimelineItem = React.memo(
  ({
    index,
    style,
    data,
  }: {
    index: number;
    style: React.CSSProperties;
    data: any;
  }) => {
    const {
      elements,
      containerWidth,
      handleTimeFrameChange,
      allElements,
      isDraggable,
      visibleTimeRange,
    } = data;
    const element = elements[index];

    // 检查元素是否在可见时间范围内
    const isVisible = useMemo(() => {
      if (!visibleTimeRange) return true;
      return (
        element.timeFrame.end >= visibleTimeRange.start &&
        element.timeFrame.start <= visibleTimeRange.end
      );
    }, [element.timeFrame, visibleTimeRange]);

    if (!isVisible) {
      return <div style={style} className="timeline-item-placeholder" />;
    }

    return (
      <div style={style} className="timeline-item timeline-track">
        <TimeFrameView
          element={element}
          containerWidth={containerWidth}
          handleTimeFrameChange={handleTimeFrameChange}
          allElements={allElements}
          isDraggable={isDraggable}
        />
      </div>
    );
  },
  (prevProps, nextProps) => {
    if (prevProps.index !== nextProps.index) return false;

    const prevData = prevProps.data;
    const nextData = nextProps.data;
    const prevElement = prevData.elements[prevProps.index];
    const nextElement = nextData.elements[nextProps.index];

    if (prevElement.id !== nextElement.id) return false;

    const prevTimeFrame = prevElement.timeFrame;
    const nextTimeFrame = nextElement.timeFrame;

    if (
      prevTimeFrame.start !== nextTimeFrame.start ||
      prevTimeFrame.end !== nextTimeFrame.end
    ) {
      return false;
    }

    if (prevData.containerWidth !== nextData.containerWidth) return false;

    const prevRange = prevData.visibleTimeRange;
    const nextRange = nextData.visibleTimeRange;

    if (!prevRange !== !nextRange) return false;
    if (
      prevRange &&
      nextRange &&
      (prevRange.start !== nextRange.start || prevRange.end !== nextRange.end)
    ) {
      return false;
    }

    return true;
  }
);

// 主时间线组件
export const TimeLine = observer(() => {
  const store = useContext(StoreContext);
  const containerRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<List>(null);
  const [containerWidth, setContainerWidth] = useState<number | null>(null);

  // 计算可见时间范围
  const visibleTimeRange = useMemo(() => {
    if (containerWidth === null) return null;

    const visibleStart = store.timelinePan.offsetX;
    const visibleEnd = visibleStart + store.timelineDisplayDuration;

    return { start: visibleStart, end: visibleEnd };
  }, [
    store.timelinePan.offsetX,
    store.timelineDisplayDuration,
    containerWidth,
  ]);

  // 获取排序后的元素
  const elements = useMemo(
    () =>
      store.editorElements
        .slice()
        .sort((a, b) => a.timeFrame.start - b.timeFrame.start),
    [store.editorElements]
  );

  // 处理时间帧变化
  const handleTimeFrameChange = useCallback(
    (element: any, start: number, end: number) => {
      store.updateEditorElementTimeFrame(element, { start, end }, true);
    },
    [store]
  );

  // 列表项数据
  const itemData = useMemo(
    () => ({
      elements,
      containerWidth,
      handleTimeFrameChange,
      allElements: elements,
      isDraggable: true,
      visibleTimeRange,
    }),
    [elements, containerWidth, handleTimeFrameChange, visibleTimeRange]
  );

  // 监听容器宽度变化
  useEffect(() => {
    setContainerWidth(getTimelineContainerWidth());

    const observer = new ResizeObserver(() => {
      const newWidth = getTimelineContainerWidth();
      setContainerWidth(newWidth);
    });

    const container = document.querySelector(".timeline-container");
    if (container) {
      observer.observe(container);
    }

    return () => {
      if (container) {
        observer.unobserve(container);
      }
      observer.disconnect();
    };
  }, []);

  return (
    <div
      ref={containerRef}
      style={{ width: "100%", height: "100%" }}
      className="timeline-elements-container"
    >
      <AutoSizer>
        {({ height, width }) => (
          <List
            ref={listRef}
            height={height}
            width={width}
            itemCount={elements.length}
            itemSize={ITEM_HEIGHT}
            itemData={itemData}
            overscanCount={OVERSCAN_COUNT}
            className="timeline-elements-list"
          >
            {TimelineItem}
          </List>
        )}
      </AutoSizer>
    </div>
  );
});
