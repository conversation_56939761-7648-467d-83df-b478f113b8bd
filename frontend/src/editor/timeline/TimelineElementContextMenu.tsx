import React from "react";
import { Menu, MenuItem, ListItemIcon, ListItemText } from "@mui/material";
import { Delete, ContentCut, ContentCopy, Edit } from "@mui/icons-material";
import { StoreContext } from "../../store";
import { EditorElement } from "../../types";

interface TimelineElementContextMenuProps {
  element: EditorElement; // 使用EditorElement类型替代any
  contextMenu: {
    mouseX: number;
    mouseY: number;
  } | null;
  handleClose: () => void;
}

const TimelineElementContextMenu: React.FC<TimelineElementContextMenuProps> = ({
  element,
  contextMenu,
  handleClose,
}) => {
  const store = React.useContext(StoreContext);

  const handleDelete = () => {
    store.removeEditorElement(element.id);
    handleClose();
  };

  const handleCut = () => {
    // 暂时使用选中和删除替代剪切功能
    store.setSelectedElement(element);
    store.removeEditorElement(element.id);
    handleClose();
  };

  const handleCopy = () => {
    // 暂时使用克隆功能替代复制功能
    store.cloneElement(element.id);
    handleClose();
  };

  const handleEdit = () => {
    store.setSelectedElement(element);
    handleClose();
  };

  return (
    <Menu
      open={contextMenu !== null}
      onClose={handleClose}
      anchorReference="anchorPosition"
      anchorPosition={
        contextMenu !== null
          ? { top: contextMenu.mouseY, left: contextMenu.mouseX }
          : undefined
      }
    >
      <MenuItem onClick={handleEdit}>
        <ListItemIcon>
          <Edit fontSize="small" />
        </ListItemIcon>
        <ListItemText>编辑</ListItemText>
      </MenuItem>
      <MenuItem onClick={handleCopy}>
        <ListItemIcon>
          <ContentCopy fontSize="small" />
        </ListItemIcon>
        <ListItemText>复制</ListItemText>
      </MenuItem>
      <MenuItem onClick={handleCut}>
        <ListItemIcon>
          <ContentCut fontSize="small" />
        </ListItemIcon>
        <ListItemText>剪切</ListItemText>
      </MenuItem>
      <MenuItem onClick={handleDelete}>
        <ListItemIcon>
          <Delete fontSize="small" />
        </ListItemIcon>
        <ListItemText>删除</ListItemText>
      </MenuItem>
    </Menu>
  );
};

export default TimelineElementContextMenu;
