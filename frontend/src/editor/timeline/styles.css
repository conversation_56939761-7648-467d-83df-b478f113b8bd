/* 拖拽相关样式 - 优化性能 */
.handle-dragging {
  /* 使用硬件加速 */
  transform: translate3d(0, -50%, 0) scale(1.2) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  /* 完全移除transition */
  will-change: transform;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.snapped {
  border-color: #ff9800 !important;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.5) !important;
  /* 完全移除transition */
  will-change: border-color, box-shadow;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.snapped-start,
.snapped-end {
  border-color: #ff9800 !important;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.5) !important;
  will-change: border-color, box-shadow;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.resize-active {
  z-index: 30 !important;
  outline: 1px dashed rgba(33, 150, 243, 0.8) !important;
  will-change: outline;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.dragging-active {
  z-index: 30 !important;
  cursor: grabbing !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  transform: translate3d(0, 0, 0) scale(1.01) !important;
  /* 完全移除transition */
  will-change: transform, box-shadow;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

.panning-mode {
  background-color: rgba(255, 152, 0, 0.2) !important;
  border: 1px dashed #ff9800 !important;
  /* 完全移除transition */
  will-change: background-color, border;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 选中元素样式 */
.element-selected {
  border: 2px dashed rgba(33, 150, 243, 0.6) !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  z-index: 20 !important;
  will-change: border, box-shadow;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 吸附指示器样式 */
.snap-indicator {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 4px;
  background-color: #ff9800;
  border-radius: 2px;
  opacity: 0.8;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.5);
  z-index: 30;
  /* 移除动画效果 */
  will-change: opacity, box-shadow;
  transform: translate3d(0, 0, 0); /* 使用translate3d强制GPU加速 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  pointer-events: none; /* 防止干扰鼠标事件 */
}

.snap-indicator-left {
  left: 0;
}

.snap-indicator-right {
  right: 0;
}

/* 基础类用于所有时间线元素 */
.timeline-element-container {
  will-change: transform, opacity;
  transform: translate3d(0, 0, 0); /* 使用translate3d强制GPU加速 */

  pointer-events: auto; /* 确保元素可以接收鼠标事件 */
}

/* 性能优化类 */
/* 主容器性能优化 */
.timeline-container {
  contain: content; /* 包含内容，提高滚动性能 */
  will-change: transform; /* 提示浏览器将要发生变换 */
  transform: translateZ(0); /* 强制GPU加速 */
  -webkit-overflow-scrolling: touch; /* 在iOS上启用惯性滚动 */
  overscroll-behavior: contain; /* 防止滚动传播 */
  backface-visibility: hidden; /* 防止3D变换时的闪烁 */
  perspective: 1000; /* 提供3D空间，提高GPU加速效果 */
}

/* 元素容器优化 */
.timeline-elements-container {
  contain: layout style; /* 包含布局和样式变化 */
  will-change: transform; /* 提示浏览器将要发生变换 */
  transform: translateZ(0); /* 强制GPU加速 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 元素列表优化 */
.timeline-elements-list {
  contain: layout style; /* 包含布局和样式变化 */
  will-change: transform; /* 提示浏览器将要发生变换 */
  transform: translateZ(0); /* 强制GPU加速 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
