import { alpha } from "@mui/material";
import { SNAP_CONSTANTS, TRACK_HEIGHT } from "./constants";

// 通用高度常量
export const CAPTION_HEIGHT = "30px";
export const ELEMENT_HEIGHT = TRACK_HEIGHT;
// 导出字幕颜色常量
export const CAPTION_COLOR = "#9c27b0"; // 紫色

// 通用控制把手样式
export const handleStyles = {
  bgcolor: "white",
  border: 2,
  borderColor: "#2196f3", // 默认使用蓝色
  borderRadius: "2px",
  width: "8px", // 增加宽度，更易点击
  height: "16px", // 增加高度，更易点击
  cursor: "ew-resize",
  transition: "all 0.15s",
  position: "absolute",
  top: "50%",
  zIndex: 50,
  transform: "translateY(-50%)",
  "&:hover": {
    bgcolor: alpha("#2196f3", 0.3),
  },
  "&.handle-dragging": {
    bgcolor: alpha("#2196f3", 0.3),
    width: "10px",
    height: "20px",
  },
  "&.snapped": {
    borderColor: SNAP_CONSTANTS.SNAP_COLOR,
    bgcolor: alpha(SNAP_CONSTANTS.SNAP_COLOR, 0.2),
    boxShadow: `0 0 8px ${alpha(SNAP_CONSTANTS.SNAP_COLOR, 0.5)}`,
  },
};

// 字幕控制把手样式
export const captionHandleStyles = {
  ...handleStyles,
  borderColor: CAPTION_COLOR,
  "&:hover": {
    bgcolor: alpha(CAPTION_COLOR, 0.3),
  },
  "&.handle-dragging": {
    bgcolor: alpha(CAPTION_COLOR, 0.3),
    width: "10px",
    height: "20px",
  },
};

// 通用内容样式函数
export const contentStyles = (color: string, isSelected: boolean) => ({
  height: "100%",
  width: "100%",
  bgcolor: isSelected ? color : alpha(color, 0.8),
  color: "white",
  fontSize: "0.75rem",
  display: "flex",
  alignItems: "center",
  justifyContent: "space-between",
  pl: 1,
  pr: 1,
  borderRadius: "4px",
  overflow: "hidden",
  textOverflow: "ellipsis",
  whiteSpace: "nowrap",
  zIndex: 5,
  cursor: "move",
  transition: "all 0.2s",
  "&:hover": {
    bgcolor: color,
  },
  userSelect: "none",
});

// 字幕内容样式
export const captionContentStyles = (isSelected: boolean) =>
  contentStyles(CAPTION_COLOR, isSelected);

// 通用时间线项目样式函数
export const timelineItemStyles = (
  color: string,
  isSelected: boolean,
  isHovering: boolean,
  isDragging: boolean = false
) => ({
  position: "absolute",
  top: 0,
  height: ELEMENT_HEIGHT,
  zIndex: isSelected ? 20 : isDragging ? 30 : 10,
  cursor: "move",
  borderRadius: 1,
  overflow: "hidden",

  boxShadow: isDragging
    ? "0 4px 8px rgba(0,0,0,0.3)"
    : isSelected
    ? "0 2px 5px rgba(0,0,0,0.2)"
    : isHovering
    ? "0 1px 3px rgba(0,0,0,0.1)"
    : "none",
  border: isSelected
    ? "2px dashed rgba(33, 150, 243, 0.8)"
    : isDragging
    ? "1px solid rgba(33, 150, 243, 0.8)"
    : "none",
  bgcolor: isSelected ? color : alpha(color, 0.8),
  "&:hover": {
    bgcolor: color,
  },
  "&.dragging-active": {
    transform: "scale(1.02)",
    boxShadow: "0 4px 8px rgba(0,0,0,0.3)",
    zIndex: 30,
    transition: "none",
  },
  "&.resize-active": {
    boxShadow: "0 2px 6px rgba(0,0,0,0.25)",
  },
  "&.panning-mode": {
    borderLeft: isDragging ? "3px solid #ff9800" : "2px solid #ff9800",
    borderRight: isDragging ? "3px solid #ff9800" : "2px solid #ff9800",
    boxShadow: "0 0 8px rgba(255,152,0,0.5)",
    bgcolor: alpha(color, 0.9),
    "&::before": {
      content: '""',
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      border: "1px dashed #ff9800",
      borderRadius: "4px",
      pointerEvents: "none",
    },
  },
  userSelect: "none",
});

// 字幕项目样式
export const captionItemStyles = (
  isSelected: boolean,
  isHovering: boolean,
  isDragging: boolean = false
) => ({
  ...timelineItemStyles(CAPTION_COLOR, isSelected, isHovering, isDragging),
  height: CAPTION_HEIGHT,
});

// 持续时间标签样式
export const durationLabelStyles = (
  compactMode: boolean,
  isVisible: boolean
) => ({
  position: "absolute" as const,
  right: compactMode ? 2 : 4,
  top: "50%",
  transform: "translateY(-50%)",
  fontSize: compactMode ? "0.7rem" : "0.8rem",
  opacity: isVisible ? 1 : 0,
  transition: "opacity 0.2s",
  pointerEvents: "none" as const,
  userSelect: "none" as const,
});

// 吸附指示器样式
export const snapIndicatorStyles = (position: "left" | "right") => ({
  position: "absolute",
  [position]: 0,
  top: "-15px",
  width: "2px",
  height: "15px",
  bgcolor: SNAP_CONSTANTS.SNAP_COLOR,
  zIndex: 60,
  boxShadow: `0 0 4px ${alpha(SNAP_CONSTANTS.SNAP_COLOR, 0.7)}`,
  "&::after": {
    content: '""',
    position: "absolute",
    top: 0,
    [position === "left" ? "left" : "right"]: "-3px",
    width: "8px",
    height: "8px",
    borderRadius: "50%",
    bgcolor: SNAP_CONSTANTS.SNAP_COLOR,
    boxShadow: `0 0 4px ${alpha(SNAP_CONSTANTS.SNAP_COLOR, 0.7)}`,
  },
  animation: "pulseSnap 1.5s infinite",
  "@keyframes pulseSnap": {
    "0%": { opacity: 0.7 },
    "50%": { opacity: 1 },
    "100%": { opacity: 0.7 },
  },
});
