import { Box, Typography } from "@mui/material";
import PlaybackSpeedControl from "./PlaybackSpeedControl";
import VolumeControl from "./VolumeControl";
import { EditorElement, AudioEditorElement } from "../../types";

interface BasicAudioProps {
  element: EditorElement | null;
}

const BasicAudio = ({ element }: BasicAudioProps) => {
  // 添加空值检查和类型检查
  if (!element || !element.properties) {
    return <></>;
  }
  // 检查是否为音频元素
  if (element.type !== "audio") {
    return <></>;
  }

  // 现在我们知道 element 是 AudioEditorElement 类型
  const audioElement = element as AudioEditorElement;

  return (
    <Box sx={{ flex: 1, display: "flex", flexDirection: "column" }}>
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          Audio
        </Typography>
      </Box>
      <Box sx={{ mb: 1, px: 1 }}>
        <VolumeControl element={audioElement} />
      </Box>
      <Box sx={{ px: 1 }}>
        <PlaybackSpeedControl element={audioElement} />
      </Box>
    </Box>
  );
};

export default BasicAudio;
