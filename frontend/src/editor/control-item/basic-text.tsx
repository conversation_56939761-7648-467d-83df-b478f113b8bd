import FormatAlignCenterIcon from "@mui/icons-material/FormatAlignCenter";
import FormatAlignLeftIcon from "@mui/icons-material/FormatAlignLeft";
import FormatAlignRightIcon from "@mui/icons-material/FormatAlignRight";
import FormatBoldIcon from "@mui/icons-material/FormatBold";
import FormatItalicIcon from "@mui/icons-material/FormatItalic";
import FormatUnderlinedIcon from "@mui/icons-material/FormatUnderlined";
import StrikethroughSIcon from "@mui/icons-material/StrikethroughS";
import {
  Box,
  Divider,
  MenuItem,
  Select,
  Stack,
  Tab,
  Tabs,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";
import { observer } from "mobx-react";
import React, { useState } from "react";
import ColorPicker from "../components/color/ColorPicker";
import { GradientPicker } from "../components/color/GradientPicker"; // 假设我们创建了这个新组件
import BaseSetting from "../setting/BaseSetting";
import { StoreContext } from "../../store";
import { EditorElement, TextEditorElement } from "../../types";

import { styled } from "@mui/material/styles";
import SliderWithInput from "./SliderWithInput";

const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));
const StyledTab = styled(Tab)(({ theme }) => ({
  fontSize: "0.8rem",
  fontWeight: theme.typography.fontWeightRegular,

  "&:hover": {
    color: theme.palette.primary.main,
    opacity: 1,
  },
  "&.Mui-selected": {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
  },
}));

interface BasicTextProps {
  element: EditorElement | null;
}

const BasicText = ({ element }: BasicTextProps) => {
  const store = React.useContext(StoreContext);

  // 将所有 hooks 移到最顶部，在任何条件检查之前
  const [fontSize, setFontSize] = useState(100);
  const [alignment, setAlignment] = useState("left");
  const [styles, setStyles] = useState([]);
  const [charSpace, setCharSpace] = useState(0);
  const [lineHeight, setLineHeight] = useState(1);
  const [fontColor, setFontColor] = useState("#ffffff");
  const [fontFamily, setFontFamily] = useState("Arial");
  const [strokeWidth, setStrokeWidth] = useState(0);
  const [strokeColor, setStrokeColor] = useState("#000000");
  const [shadowColor, setShadowColor] = useState("#000000");
  const [shadowBlur, setShadowBlur] = useState(0);
  const [shadowOffsetX, setShadowOffsetX] = useState(0);
  const [shadowOffsetY, setShadowOffsetY] = useState(0);
  const [gradientColors, setGradientColors] = useState(["#ffffff", "#000000"]);
  const [useGradient, setUseGradient] = useState(false);
  const [backgroundColor, setBackgroundColor] = useState("transparent");
  const [activeTab, setActiveTab] = useState(0);

  // 使用 useEffect 来同步状态
  React.useEffect(() => {
    if (element?.type === "text" && element?.properties) {
      const textElement = element as TextEditorElement;
      setFontSize(textElement.properties.fontSize || 100);
      setAlignment(textElement.properties.textAlign || "left");
      setStyles(textElement.properties.styles || []);
      setCharSpace(textElement.properties.charSpacing || 0);
      setLineHeight(textElement.properties.lineHeight || 1);
      setFontColor(textElement.properties.fontColor || "#ffffff");
      setFontFamily(textElement.properties.fontFamily || "Arial");
      setStrokeWidth(textElement.properties.strokeWidth || 0);
      setStrokeColor(textElement.properties.strokeColor || "#000000");
      setShadowColor(textElement.properties.shadowColor || "#000000");
      setShadowBlur(textElement.properties.shadowBlur || 0);
      setShadowOffsetX(textElement.properties.shadowOffsetX || 0);
      setShadowOffsetY(textElement.properties.shadowOffsetY || 0);
      setGradientColors(
        textElement.properties.gradientColors || ["#ffffff", "#000000"]
      );
      setUseGradient(textElement.properties.useGradient || false);
      setBackgroundColor(
        textElement.properties.backgroundColor || "transparent"
      );
    }
  }, [element]);

  React.useEffect(() => {
    return () => {};
  }, []);

  // 添加空值检查和类型检查
  if (!element || !element.properties) {
    return <></>;
  }

  // 检查是否为文本元素
  if (element.type !== "text") {
    return <></>;
  }

  // 现在我们知道 element 是 TextEditorElement 类型
  const textElement = element as TextEditorElement;

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleAlignmentChange = (event, newAlignment) => {
    if (newAlignment !== null) {
      setAlignment(newAlignment);
      store.updateTextStyle(textElement.id, { textAlign: newAlignment });
    }
  };

  const handleStyleChange = (event, newStyles) => {
    setStyles(newStyles);
    store.updateTextStyle(textElement.id, { styles: newStyles });
  };

  const handleColorChange = (color) => {
    if (useGradient) {
      // 如果使用渐变，更新渐变色
      const newGradientColors = [...gradientColors];
      newGradientColors[0] = color;
      setGradientColors(newGradientColors);
      store.updateTextStyle(textElement.id, {
        gradientColors: newGradientColors,
        useGradient: true,
      });
    } else {
      // 如果不使用渐变，更新普通颜色
      setFontColor(color);
      store.updateTextStyle(textElement.id, {
        fontColor: color,
        useGradient: false,
      });
    }
  };

  const handleGradientChange = (newGradientColors) => {
    setGradientColors(newGradientColors);
    store.updateTextStyle(textElement.id, {
      gradientColors: newGradientColors,
      useGradient: true,
    });
  };

  const handleFontFamilyChange = (event) => {
    const newFontFamily = event.target.value as string;
    setFontFamily(newFontFamily);
    store.updateTextStyle(textElement.id, { fontFamily: newFontFamily });
  };

  const fontFamilies = [
    "Arial",
    "Helvetica",
    "Times New Roman",
    "Courier",
    "Verdana",
    "Georgia",
    "Palatino",
    "Garamond",
    "Bookman",
    "Comic Sans MS",
    "Trebuchet MS",
    "Arial Black",
    "Impact",
    // Added new fonts
    "Roboto",
  ];

  const StyledSelect = styled(Select)({
    "& .MuiSelect-select": {
      fontSize: "0.9rem",
    },
  });

  const StyledMenuItem = styled(MenuItem)({
    fontSize: "0.9rem",
    display: "flex",
    alignItems: "center",
    "&:hover": {
      backgroundColor: "#f0f0f0",
    },
  });

  const FontPreview = styled("span")({
    marginRight: "8px",
    fontSize: "0.9rem",
  });

  return (
    <Box
      sx={{
        width: "100%",
        flex: 1,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          Text
        </Typography>
      </Box>
      {/* <Divider /> */}
      <StyledTabs value={activeTab} onChange={handleTabChange}>
        <StyledTab label="Basic" />
        <StyledTab label="Advanced" />
      </StyledTabs>
      <Box
        sx={{
          m: 2,
          width: "250px",
          height: "100%",
          overflow: "auto",
          pr: 2,
          "&::-webkit-scrollbar": {
            width: "1px",
          },
          "&::-webkit-scrollbar-track": {
            background: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(255, 255, 255, 0.2)",
            borderRadius: "1px",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.3)",
            },
          },
        }}
      >
        <Stack spacing={2}>
          {activeTab === 0 && (
            <>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                  }}
                >
                  Font Family
                </Typography>
                <StyledSelect
                  value={fontFamily}
                  onChange={handleFontFamilyChange}
                  sx={{ width: "50%" }}
                  size="small"
                >
                  {fontFamilies.map((font) => (
                    <StyledMenuItem key={font} value={font}>
                      <FontPreview style={{ fontFamily: font }}>
                        {font}
                      </FontPreview>
                    </StyledMenuItem>
                  ))}
                </StyledSelect>
              </Stack>

              <SliderWithInput
                label="Font Size"
                value={fontSize}
                onChange={setFontSize}
                onChangeCommitted={(newValue) => {
                  store.updateTextStyle(textElement.id, {
                    fontSize: newValue,
                  });
                }}
                min={0}
                max={200}
              />

              <Stack
                direction="row"
                spacing={2}
                alignItems="center"
                justifyContent="space-between"
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                  }}
                >
                  Color
                </Typography>
                {useGradient ? (
                  <GradientPicker
                    colors={gradientColors}
                    onChange={handleGradientChange}
                  />
                ) : (
                  <ColorPicker color={fontColor} onChange={handleColorChange} />
                )}
                {/* <FormControlLabel
                  control={
                    <Checkbox
                      size="small"
                      checked={useGradient}
                      onChange={(e) => {
                        setUseGradient(e.target.checked);
                        if (e.target.checked)
                          store.updateTextStyle(textElement.id, {
                            gradientColors: gradientColors,
                            useGradient: true,
                          });
                        else
                          store.updateTextStyle(textElement.id, {
                            fontColor: fontColor,
                            useGradient: true,
                          });
                      }}
                    />
                  }
                  label={<Typography variant="body2">Gradient</Typography>}
                /> */}
              </Stack>
              <Stack
                direction="row"
                spacing={2}
                alignItems="center"
                justifyContent="space-between"
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                  }}
                >
                  Background
                </Typography>
                <ColorPicker
                  color={backgroundColor}
                  onChange={(color) => {
                    setBackgroundColor(color);
                    store.updateTextStyle(textElement.id, {
                      backgroundColor: color,
                    });
                  }}
                />
              </Stack>
              <SliderWithInput
                label="Character"
                value={charSpace}
                onChange={setCharSpace}
                onChangeCommitted={(newValue) => {
                  store.updateTextStyle(textElement.id, {
                    charSpacing: newValue,
                  });
                }}
                min={-200}
                max={800}
                step={0.1}
              />

              <SliderWithInput
                label="Line"
                value={lineHeight}
                onChange={setLineHeight}
                onChangeCommitted={(newValue) => {
                  store.updateTextStyle(textElement.id, {
                    lineHeight: newValue,
                  });
                }}
                min={-2}
                max={2}
                step={0.01}
              />

              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                  }}
                >
                  Alignment
                </Typography>
                <ToggleButtonGroup
                  value={alignment}
                  exclusive
                  onChange={handleAlignmentChange}
                  aria-label="text alignment"
                  size="small"
                >
                  <ToggleButton value="left" aria-label="left aligned">
                    <FormatAlignLeftIcon fontSize="small" />
                  </ToggleButton>
                  <ToggleButton value="center" aria-label="centered">
                    <FormatAlignCenterIcon fontSize="small" />
                  </ToggleButton>
                  <ToggleButton value="right" aria-label="right aligned">
                    <FormatAlignRightIcon fontSize="small" />
                  </ToggleButton>
                </ToggleButtonGroup>
              </Stack>
              <Divider />
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                  }}
                >
                  Style
                </Typography>
                <ToggleButtonGroup
                  value={styles}
                  onChange={handleStyleChange}
                  aria-label="text styling"
                  size="small"
                >
                  <ToggleButton value="bold" aria-label="bold" size="small">
                    <FormatBoldIcon fontSize="small" />
                  </ToggleButton>
                  <ToggleButton value="italic" aria-label="italic" size="small">
                    <FormatItalicIcon fontSize="small" />
                  </ToggleButton>
                  <ToggleButton
                    value="underlined"
                    aria-label="underlined"
                    size="small"
                  >
                    <FormatUnderlinedIcon fontSize="small" />
                  </ToggleButton>
                  <ToggleButton
                    value="strikethrough"
                    aria-label="strikethrough"
                    size="small"
                  >
                    <StrikethroughSIcon fontSize="small" />
                  </ToggleButton>
                </ToggleButtonGroup>
              </Stack>
              <Divider />
              <BaseSetting element={textElement} />
            </>
          )}
          {activeTab === 1 && (
            <>
              <SliderWithInput
                label="Stroke Width"
                value={strokeWidth}
                onChange={setStrokeWidth}
                onChangeCommitted={(newValue) => {
                  store.updateTextStyle(textElement.id, {
                    strokeWidth: newValue,
                  });
                }}
                min={0}
                max={5}
                step={0.2}
              />

              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                  }}
                >
                  Stroke Color
                </Typography>
                <ColorPicker
                  color={strokeColor}
                  onChange={(color) => {
                    setStrokeColor(color);
                    store.updateTextStyle(textElement.id, {
                      strokeColor: color,
                    });
                  }}
                />
              </Stack>

              <Divider />

              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="body2"
                  sx={{
                    color: "text.secondary",
                  }}
                >
                  Shadow Color
                </Typography>
                <ColorPicker
                  color={shadowColor}
                  onChange={(color) => {
                    setShadowColor(color);
                    store.updateTextStyle(textElement.id, {
                      shadowColor: color,
                    });
                  }}
                />
              </Stack>

              <SliderWithInput
                label="Shadow Blur"
                value={shadowBlur}
                onChange={setShadowBlur}
                onChangeCommitted={(newValue) => {
                  store.updateTextStyle(textElement.id, {
                    shadowBlur: newValue,
                  });
                }}
                min={0}
                max={10}
              />

              <SliderWithInput
                label="Offset X"
                value={shadowOffsetX}
                onChange={setShadowOffsetX}
                onChangeCommitted={(newValue) => {
                  store.updateTextStyle(textElement.id, {
                    shadowOffsetX: newValue,
                  });
                }}
                min={-50}
                max={50}
              />

              <SliderWithInput
                label="Offset Y"
                value={shadowOffsetY}
                onChange={setShadowOffsetY}
                onChangeCommitted={(newValue) => {
                  store.updateTextStyle(textElement.id, {
                    shadowOffsetY: newValue,
                  });
                }}
                min={-20}
                max={20}
              />
            </>
          )}
        </Stack>
      </Box>
    </Box>
  );
};

export default BasicText;
