import BorderAllIcon from "@mui/icons-material/BorderAll";
import CancelIcon from "@mui/icons-material/Cancel";
import CropIcon from "@mui/icons-material/Crop";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import {
  Box,
  IconButton,
  MenuItem,
  Select,
  SelectChangeEvent,
  Slider,
  Stack,
  Tab,
  Tabs,
  Tooltip,
  Typography,
  Divider,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { observer } from "mobx-react";
import React from "react";
import { StoreContext } from "../../store";
import {
  BorderStyle,
  EffecType,
  ImageEditorElement,
  EditorElement,
} from "../../types";
import ColorPicker from "../components/color/ColorPicker";
import BaseSetting from "../setting/BaseSetting";
import SliderWithInput from "./SliderWithInput";
import FilterControl from "./FilterControl";
const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(1),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  fontSize: "0.8rem",
  fontWeight: theme.typography.fontWeightRegular,

  "&:hover": {
    color: theme.palette.primary.main,
    opacity: 1,
  },
  "&.Mui-selected": {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
  },
}));

const EFFECT_TYPE_TO_LABEL: Record<string, string> = {
  none: "None",
  blackAndWhite: "Black and White",
  saturate: "Saturate",
  sepia: "Sepia",
  invert: "Invert",
};

export type EffectResourceProps = {
  editorElement: ImageEditorElement;
};

interface ImageSettingProps {
  element: EditorElement | null;
}

const ImageSetting = observer(({ element }: ImageSettingProps) => {
  const store = React.useContext(StoreContext);

  if (!element || !element.properties) {
    return <></>;
  }

  if (element.type !== "image") {
    return <></>;
  }

  const imageElement = element as ImageEditorElement;

  console.log(imageElement.properties.filters);
  const [isCropping, setIsCropping] = React.useState(false);
  const [activeTab, setActiveTab] = React.useState(0);
  const [showBorderSettings, setShowBorderSettings] = React.useState(false);

  const handleCrop = () => {
    if (!isCropping) {
      store.startCropMode(imageElement.id);
      setIsCropping(true);
    } else {
      store.applyCrop();
      setIsCropping(false);
    }
  };

  const handleCancelCrop = () => {
    store.cancelCrop();
    setIsCropping(false);
  };

  const updateFilter = (id: string, filterType: string, value: number) => {
    store.setMediaFilter(
      id,
      filterType as "brightness" | "contrast" | "saturation" | "hue" | "blur",
      value
    );
  };

  const handleResetFilters = () => {
    const defaultFilters = {
      brightness: 0,
      contrast: 0,
      saturation: 0,
      hue: 0,
      blur: 0,
    };

    Object.entries(defaultFilters).forEach(([filterType, value]) => {
      updateFilter(imageElement.id, filterType, value);
    });
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleBorderToggle = () => {
    setShowBorderSettings(!showBorderSettings);
  };

  const updateBorder = (
    property: keyof BorderStyle,
    value: string | number
  ) => {
    store.setMediaElementBorder(imageElement.id, property, value);
  };

  return (
    <Box
      sx={{
        width: "100%",
        flex: 1,
        display: "flex",
        flexDirection: "column",
      }}
    >
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          flexShrink: 0,
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          Image
        </Typography>
      </Box>
      <Divider />
      <Box
        sx={{
          m: 2,
          width: "250px",
          height: "100%",
          overflow: "auto",
          pr: 2,
          "&::-webkit-scrollbar": {
            width: "1px",
          },
          "&::-webkit-scrollbar-track": {
            background: "transparent",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(255, 255, 255, 0.1)",
            borderRadius: "2px",
            "&:hover": {
              backgroundColor: "rgba(255, 255, 255, 0.1)",
            },
          },
        }}
      >
        <StyledTabs value={activeTab} onChange={handleTabChange}>
          <StyledTab label="Basic" />
          <StyledTab label="Advanced" />
        </StyledTabs>

        {activeTab === 0 && (
          <Box>
            <Stack direction="row" spacing={2} sx={{ mb: 1 }}>
              <Tooltip title={isCropping ? "Apply Crop" : "Crop Image"}>
                <IconButton
                  onClick={handleCrop}
                  color={isCropping ? "secondary" : "primary"}
                >
                  <CropIcon fontSize="small" />
                </IconButton>
              </Tooltip>
              {isCropping && (
                <Tooltip title="Cancel Crop">
                  <IconButton onClick={handleCancelCrop}>
                    <CancelIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              )}
              <Tooltip
                title={showBorderSettings ? "Hide Settings" : "Show Settings"}
              >
                <IconButton
                  onClick={handleBorderToggle}
                  color={showBorderSettings ? "secondary" : "primary"}
                >
                  <BorderAllIcon />
                </IconButton>
              </Tooltip>
            </Stack>
            {showBorderSettings && (
              <Box sx={{ mb: 3 }}>
                <Stack spacing={2}>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px" }}
                    >
                      Width
                    </Typography>
                    <Slider
                      size="small"
                      value={imageElement.properties.border?.width ?? 0}
                      min={0}
                      max={20}
                      onChange={(e, newValue) =>
                        updateBorder("width", Number(newValue))
                      }
                      sx={{ flex: 1 }}
                    />
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px" }}
                    >
                      Color
                    </Typography>
                    <ColorPicker
                      color={imageElement.properties.border?.color ?? "#000000"}
                      onChange={(color) => updateBorder("color", color)}
                    />
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px" }}
                    >
                      Style
                    </Typography>
                    <Select
                      size="small"
                      value={imageElement.properties.border?.style ?? "solid"}
                      onChange={(e) => updateBorder("style", e.target.value)}
                      fullWidth
                      renderValue={(selected) => (
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              mr: 1,
                              border: `2px ${selected} black`,
                            }}
                          />
                          <Typography variant="body1" sx={{ fontSize: "1rem" }}>
                            {selected.charAt(0).toUpperCase() +
                              selected.slice(1)}
                          </Typography>
                        </Box>
                      )}
                    >
                      <MenuItem value="solid">
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              mr: 1,
                              border: "2px solid black",
                            }}
                          />
                          <Typography variant="body1" sx={{ fontSize: "1rem" }}>
                            Solid
                          </Typography>
                        </Box>
                      </MenuItem>
                      <MenuItem value="dashed">
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              mr: 1,
                              border: "2px dashed black",
                            }}
                          />
                          <Typography variant="body1" sx={{ fontSize: "1rem" }}>
                            Dashed
                          </Typography>
                        </Box>
                      </MenuItem>
                      <MenuItem value="dotted">
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              mr: 1,
                              border: "2px dotted black",
                            }}
                          />
                          <Typography variant="body1" sx={{ fontSize: "1rem" }}>
                            Dotted
                          </Typography>
                        </Box>
                      </MenuItem>
                    </Select>
                  </Box>
                  <Box sx={{ display: "flex", alignItems: "center" }}>
                    <Typography
                      variant="body2"
                      sx={{ mr: 2, minWidth: "40px" }}
                    >
                      Radius
                    </Typography>
                    <Slider
                      size="small"
                      value={imageElement.properties.border?.borderRadius ?? 0}
                      min={0}
                      max={100}
                      onChange={(e, newValue) =>
                        updateBorder("borderRadius", Number(newValue))
                      }
                      sx={{ flex: 1 }}
                    />
                  </Box>
                </Stack>
              </Box>
            )}
            <BaseSetting element={imageElement} />
          </Box>
        )}

        {activeTab === 1 && (
          <Box>
            <FilterControl element={imageElement} />
          </Box>
        )}
      </Box>
    </Box>
  );
});

export default ImageSetting;
