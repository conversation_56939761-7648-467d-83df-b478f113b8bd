import React from "react";
import {
  Box,
  IconButton,
  Select,
  MenuItem,
  Typography,
  Stack,
} from "@mui/material";
import SliderWithInput from "./SliderWithInput";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import { StoreContext } from "../../store";
import { EffecType, ImageEditorElement, EditorElement } from "../../types";
import { observer } from "mobx-react";

const EFFECT_TYPE_TO_LABEL: Record<string, string> = {
  none: "None",
  blackAndWhite: "Black and White",
  saturate: "Saturate",
  sepia: "Sepia",
  invert: "Invert",
};

interface FilterControlProps {
  element: EditorElement | null;
}

const FilterControl = observer(({ element }: FilterControlProps) => {
  const store = React.useContext(StoreContext);

  if (!element || !element.properties) {
    return <></>;
  }

  if (element.type !== "image") {
    return <></>;
  }

  const imageElement = element as ImageEditorElement;

  const updateFilter = (filterType: string, value: number) => {
    store.setMediaFilter(
      imageElement.id,
      filterType as "brightness" | "contrast" | "saturation" | "hue" | "blur",
      value
    );
  };

  const handleResetFilters = () => {
    const defaultFilters = {
      brightness: 0,
      contrast: 0,
      saturation: 0,
      hue: 0,
      blur: 0,
    };

    Object.entries(defaultFilters).forEach(([filterType, value]) => {
      updateFilter(filterType, value);
    });
  };

  return (
    <Box>
      <Stack
        direction="row"
        spacing={1}
        sx={{
          justifyContent: "flex-start",
          alignItems: "center",
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
          Filters
        </Typography>
        <IconButton onClick={handleResetFilters} size="small">
          <RestartAltIcon />
        </IconButton>
      </Stack>
      <Box sx={{ mb: 2 }}>
        <Select
          size="small"
          value={imageElement.properties.effect?.type}
          onChange={(e) => {
            const type = e.target.value;
            store.updateEffect(imageElement.id, {
              type: type as EffecType,
            });
          }}
          sx={{
            width: "100%",
          }}
        >
          {Object.entries(EFFECT_TYPE_TO_LABEL).map(([type, label]) => (
            <MenuItem key={type} value={type}>
              {label}
            </MenuItem>
          ))}
        </Select>
      </Box>
      {["Brightness", "Contrast", "Saturation", "Hue", "Blur"].map((filter) => (
        <Box key={filter} sx={{ mb: 1.5 }}>
          <SliderWithInput
            label={filter}
            value={imageElement.properties.filters?.[filter.toLowerCase()] ?? 0}
            min={filter === "Blur" ? 0 : -100}
            max={100}
            onChange={(newValue) => {
              // Do nothing during drag or direct input
            }}
            onChangeCommitted={(newValue) =>
              updateFilter(filter.toLowerCase(), newValue)
            }
          />
        </Box>
      ))}
    </Box>
  );
});

export default FilterControl;
