import { useState } from "react";
import { Box, Typography, Slider, TextField, IconButton } from "@mui/material";
import RefreshIcon from "@mui/icons-material/Refresh";

interface TransformControlProps {
  label: string;
  children: React.ReactNode;
}

const TransformControl = ({ label, children }: TransformControlProps) => (
  <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
    <Typography variant="body2" color="text.secondary">
      {label}
    </Typography>
    {children}
  </Box>
);

const Transform = () => {
  const [value, setValue] = useState<number>(10);

  const handleSliderChange = (event: Event, newValue: number | number[]) => {
    setValue(newValue as number);
  };

  const ResetButton = () => (
    <IconButton
      size="small"
      sx={{
        width: 24,
        height: 24,
        color: "text.secondary",
        "&:hover": {
          color: "text.primary",
          bgcolor: "action.hover",
        },
      }}
    >
      <RefreshIcon sx={{ fontSize: 14 }} />
    </IconButton>
  );

  const textFieldProps = {
    size: "small" as const,
    defaultValue: 100,
    inputProps: {
      style: {
        padding: "4px",
        fontSize: "0.875rem",
      },
    },
    sx: {
      "& .MuiOutlinedInput-root": {
        "& fieldset": {
          borderColor: "divider",
        },
      },
    },
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      <Typography variant="body2">Transform</Typography>

      <TransformControl label="Scale">
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "1fr 40px 24px",
            gap: 0.5,
            alignItems: "center",
          }}
        >
          <Slider
            value={value}
            onChange={handleSliderChange}
            max={1}
            step={0.1}
            aria-label="Scale"
            sx={{
              "& .MuiSlider-thumb": {
                width: 12,
                height: 12,
              },
              "& .MuiSlider-rail": {
                opacity: 0.3,
              },
            }}
          />
          <TextField
            {...textFieldProps}
            inputProps={{
              ...textFieldProps.inputProps,
              style: {
                ...textFieldProps.inputProps.style,
                textAlign: "center",
              },
            }}
          />
          <ResetButton />
        </Box>
      </TransformControl>

      <TransformControl label="Position">
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr 24px",
            gap: 0.5,
            alignItems: "center",
          }}
        >
          <Box sx={{ position: "relative" }}>
            <TextField {...textFieldProps} fullWidth />
            <Typography
              sx={{
                position: "absolute",
                top: "50%",
                right: 10,
                transform: "translateY(-50%)",
                color: "text.secondary",
              }}
              variant="body2"
            >
              x
            </Typography>
          </Box>
          <Box sx={{ position: "relative" }}>
            <TextField {...textFieldProps} fullWidth />
            <Typography
              sx={{
                position: "absolute",
                top: "50%",
                right: 10,
                transform: "translateY(-50%)",
                color: "text.secondary",
              }}
              variant="body2"
            >
              y
            </Typography>
          </Box>
          <ResetButton />
        </Box>
      </TransformControl>

      <TransformControl label="Rotate">
        <Box
          sx={{
            display: "grid",
            gridTemplateColumns: "1fr 1fr 24px",
            gap: 0.5,
            alignItems: "center",
          }}
        >
          <TextField {...textFieldProps} />
          <Box />
          <ResetButton />
        </Box>
      </TransformControl>
    </Box>
  );
};

export default Transform;
