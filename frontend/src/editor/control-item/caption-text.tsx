import React, { useState, useEffect, useContext } from "react";
import {
  <PERSON>,
  Typography,
  Stack,
  Tabs,
  Tab,
  Divider,
  Select,
  MenuItem,
  ToggleButton,
  ToggleButtonGroup,
  styled,
  Button,
} from "@mui/material";
import {
  FormatBold,
  FormatItalic,
  FormatUnderlined,
  FormatAlignLeft,
  FormatAlignCenter,
  FormatAlignRight,
} from "@mui/icons-material";
import { observer } from "mobx-react-lite";
import { StoreContext } from "../../store";
import { Caption, CaptionStyle } from "../../types";
import ColorPicker from "../components/color/ColorPicker";
import SliderWithInput from "./SliderWithInput";

// 样式化组件
const StyledSelect = styled(Select)(({ theme }) => ({
  "& .MuiOutlinedInput-notchedOutline": {
    borderColor: theme.palette.divider,
  },
  "&:hover .MuiOutlinedInput-notchedOutline": {
    borderColor: theme.palette.primary.main,
  },
  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
    borderColor: theme.palette.primary.main,
  },
}));

const StyledMenuItem = styled(MenuItem)({
  fontSize: "0.875rem",
});

const FontPreview = styled("span")({
  fontSize: "0.875rem",
});

// 字体列表
const fontFamilies = [
  "Arial",
  "Helvetica",
  "Times New Roman",
  "Georgia",
  "Verdana",
  "Courier New",
  "Impact",
  "Comic Sans MS",
  "Trebuchet MS",
  "Arial Black",
  "Palatino",
  "Garamond",
  "Bookman",
  "Avant Garde",
  "Roboto",
  "Open Sans",
  "Lato",
  "Montserrat",
  "Source Sans Pro",
  "Oswald",
  "Raleway",
  "PT Sans",
  "Ubuntu",
  "Merriweather",
  "Playfair Display",
];

interface CaptionTextProps {
  // 不再需要传入特定的caption，因为我们使用全局样式
}

const CaptionText = observer(({}: CaptionTextProps) => {
  const store = useContext(StoreContext);
  const [activeTab, setActiveTab] = useState(0);

  // 获取全局字幕样式
  const globalStyle = store.getGlobalCaptionStyle();

  // 状态管理 - 使用全局样式
  const [fontSize, setFontSize] = useState(globalStyle.fontSize);
  const [alignment, setAlignment] = useState(globalStyle.textAlign);
  const [styles, setStyles] = useState(globalStyle.styles);
  const [charSpace, setCharSpace] = useState(globalStyle.charSpacing);
  const [lineHeight, setLineHeight] = useState(globalStyle.lineHeight);
  const [fontColor, setFontColor] = useState(globalStyle.fontColor);
  const [fontFamily, setFontFamily] = useState(globalStyle.fontFamily);
  const [strokeWidth, setStrokeWidth] = useState(globalStyle.strokeWidth);
  const [strokeColor, setStrokeColor] = useState(globalStyle.strokeColor);
  const [shadowColor, setShadowColor] = useState(globalStyle.shadowColor);
  const [shadowBlur, setShadowBlur] = useState(globalStyle.shadowBlur);
  const [shadowOffsetX, setShadowOffsetX] = useState(globalStyle.shadowOffsetX);
  const [shadowOffsetY, setShadowOffsetY] = useState(globalStyle.shadowOffsetY);
  const [backgroundColor, setBackgroundColor] = useState(
    globalStyle.backgroundColor
  );
  const [positionX, setPositionX] = useState(globalStyle.positionX || 0);
  const [positionY, setPositionY] = useState(globalStyle.positionY || 0);

  // 同步全局样式变化
  useEffect(() => {
    const globalStyle = store.getGlobalCaptionStyle();
    setFontSize(globalStyle.fontSize);
    setAlignment(globalStyle.textAlign);
    setStyles(globalStyle.styles);
    setCharSpace(globalStyle.charSpacing);
    setLineHeight(globalStyle.lineHeight);
    setFontColor(globalStyle.fontColor);
    setFontFamily(globalStyle.fontFamily);
    setStrokeWidth(globalStyle.strokeWidth);
    setStrokeColor(globalStyle.strokeColor);
    setShadowColor(globalStyle.shadowColor);
    setShadowBlur(globalStyle.shadowBlur);
    setShadowOffsetX(globalStyle.shadowOffsetX);
    setShadowOffsetY(globalStyle.shadowOffsetY);
    setBackgroundColor(globalStyle.backgroundColor);
    setPositionX(globalStyle.positionX || 0);
    setPositionY(globalStyle.positionY || 0);
  }, [store.captionManager.globalCaptionStyle]);

  // 事件处理器
  const handleFontFamilyChange = (event: any) => {
    const newFontFamily = event.target.value;
    setFontFamily(newFontFamily);
    store.updateGlobalCaptionStyle({ fontFamily: newFontFamily });
  };

  const handleAlignmentChange = (event: any, newAlignment: string) => {
    if (newAlignment !== null) {
      const alignmentValue = newAlignment as "left" | "center" | "right";
      setAlignment(alignmentValue);
      store.updateGlobalCaptionStyle({
        textAlign: alignmentValue,
      });
    }
  };

  const handleStyleChange = (event: any, newStyles: string[]) => {
    setStyles(newStyles);
    const fontWeight = newStyles.includes("bold") ? 700 : 400;
    store.updateGlobalCaptionStyle({
      styles: newStyles,
      fontWeight: fontWeight,
    });
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  return (
    <Box sx={{ width: "100%" }}>
      <Tabs
        value={activeTab}
        onChange={handleTabChange}
        variant="fullWidth"
        sx={{
          borderBottom: 1,
          borderColor: "divider",
          "& .MuiTab-root": {
            minHeight: 40,
            fontSize: "0.75rem",
            textTransform: "none",
          },
        }}
      >
        <Tab label="字体" />
        <Tab label="效果" />
        <Tab label="位置" />
      </Tabs>

      <Box sx={{ p: 2 }}>
        <Stack spacing={2}>
          {activeTab === 0 && (
            <>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  字体
                </Typography>
                <StyledSelect
                  value={fontFamily}
                  onChange={handleFontFamilyChange}
                  sx={{ width: "50%" }}
                  size="small"
                >
                  {fontFamilies.map((font) => (
                    <StyledMenuItem key={font} value={font}>
                      <FontPreview style={{ fontFamily: font }}>
                        {font}
                      </FontPreview>
                    </StyledMenuItem>
                  ))}
                </StyledSelect>
              </Stack>

              <SliderWithInput
                label="字体大小"
                value={fontSize}
                onChange={setFontSize}
                onChangeCommitted={(newValue) => {
                  store.updateGlobalCaptionStyle({ fontSize: newValue });
                }}
                min={10}
                max={200}
              />

              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  对齐方式
                </Typography>
                <ToggleButtonGroup
                  value={alignment}
                  exclusive
                  onChange={handleAlignmentChange}
                  size="small"
                >
                  <ToggleButton value="left">
                    <FormatAlignLeft fontSize="small" />
                  </ToggleButton>
                  <ToggleButton value="center">
                    <FormatAlignCenter fontSize="small" />
                  </ToggleButton>
                  <ToggleButton value="right">
                    <FormatAlignRight fontSize="small" />
                  </ToggleButton>
                </ToggleButtonGroup>
              </Stack>

              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  样式
                </Typography>
                <ToggleButtonGroup
                  value={styles}
                  onChange={handleStyleChange}
                  size="small"
                >
                  <ToggleButton value="bold">
                    <FormatBold fontSize="small" />
                  </ToggleButton>
                  <ToggleButton value="italic">
                    <FormatItalic fontSize="small" />
                  </ToggleButton>
                  <ToggleButton value="underline">
                    <FormatUnderlined fontSize="small" />
                  </ToggleButton>
                </ToggleButtonGroup>
              </Stack>

              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  字体颜色
                </Typography>
                <ColorPicker
                  color={fontColor}
                  onChange={(color) => {
                    setFontColor(color);
                    store.updateGlobalCaptionStyle({ fontColor: color });
                  }}
                />
              </Stack>

              <SliderWithInput
                label="字符间距"
                value={charSpace}
                onChange={setCharSpace}
                onChangeCommitted={(newValue) => {
                  store.updateGlobalCaptionStyle({
                    charSpacing: newValue,
                  });
                }}
                min={-50}
                max={100}
              />

              <SliderWithInput
                label="行高"
                value={lineHeight}
                onChange={setLineHeight}
                onChangeCommitted={(newValue) => {
                  store.updateGlobalCaptionStyle({
                    lineHeight: newValue,
                  });
                }}
                min={0.5}
                max={3}
                step={0.1}
              />
            </>
          )}

          {activeTab === 1 && (
            <>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  描边颜色
                </Typography>
                <ColorPicker
                  color={strokeColor}
                  onChange={(color) => {
                    setStrokeColor(color);
                    store.updateGlobalCaptionStyle({
                      strokeColor: color,
                    });
                  }}
                />
              </Stack>

              <SliderWithInput
                label="描边宽度"
                value={strokeWidth}
                onChange={setStrokeWidth}
                onChangeCommitted={(newValue) => {
                  store.updateGlobalCaptionStyle({
                    strokeWidth: newValue,
                  });
                }}
                min={0}
                max={10}
                step={0.1}
              />

              <Divider />

              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  阴影颜色
                </Typography>
                <ColorPicker
                  color={shadowColor}
                  onChange={(color) => {
                    setShadowColor(color);
                    store.updateGlobalCaptionStyle({
                      shadowColor: color,
                    });
                  }}
                />
              </Stack>

              <SliderWithInput
                label="阴影模糊"
                value={shadowBlur}
                onChange={setShadowBlur}
                onChangeCommitted={(newValue) => {
                  store.updateGlobalCaptionStyle({
                    shadowBlur: newValue,
                  });
                }}
                min={0}
                max={10}
              />

              <SliderWithInput
                label="阴影偏移 X"
                value={shadowOffsetX}
                onChange={setShadowOffsetX}
                onChangeCommitted={(newValue) => {
                  store.updateGlobalCaptionStyle({
                    shadowOffsetX: newValue,
                  });
                }}
                min={-50}
                max={50}
              />

              <SliderWithInput
                label="阴影偏移 Y"
                value={shadowOffsetY}
                onChange={setShadowOffsetY}
                onChangeCommitted={(newValue) => {
                  store.updateGlobalCaptionStyle({
                    shadowOffsetY: newValue,
                  });
                }}
                min={-20}
                max={20}
              />
            </>
          )}

          {activeTab === 2 && (
            <>
              <Typography
                variant="body2"
                sx={{ color: "text.secondary", mb: 1 }}
              >
                位置设置（可在画布中直接拖拽字幕调整位置）
              </Typography>

              <SliderWithInput
                label="水平位置"
                value={positionX}
                onChange={setPositionX}
                onChangeCommitted={(newValue) => {
                  store.updateGlobalCaptionStyle({
                    positionX: newValue,
                  });
                }}
                min={-500}
                max={500}
              />

              <SliderWithInput
                label="垂直位置"
                value={positionY}
                onChange={setPositionY}
                onChangeCommitted={(newValue) => {
                  store.updateGlobalCaptionStyle({
                    positionY: newValue,
                  });
                }}
                min={-300}
                max={300}
              />

              <Typography
                variant="caption"
                sx={{ color: "text.secondary", mt: 1 }}
              >
                提示：正值向右/向下移动，负值向左/向上移动
              </Typography>

              <Button
                variant="outlined"
                size="small"
                onClick={() => {
                  store.debugCaptionPosition();
                }}
                sx={{ mt: 2 }}
              >
                调试字幕位置
              </Button>
              <Typography
                variant="caption"
                sx={{ color: "text.secondary", mt: 1, display: "block" }}
              >
                点击此按钮在控制台查看字幕位置信息，验证前端和后端位置是否一致
              </Typography>
            </>
          )}
        </Stack>
      </Box>
    </Box>
  );
});

export default CaptionText;
