import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Field, Stack, Typography } from "@mui/material";

interface SliderWithInputProps {
  label: string;
  value: number;
  onChange: (value: number) => void;
  onChangeCommitted?: (value: number) => void;
  min: number;
  max: number;
  step?: number;
  width?: string;
  textFieldWidth?: string;
}

const SliderWithInput = ({
  label,
  value,
  onChange,
  onChangeCommitted,
  min,
  max,
  step = 1,
  textFieldWidth = "50px",
}: SliderWithInputProps) => {
  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      alignItems="center"
      spacing={0.5}
    >
      <Typography
        variant="body2"
        sx={{
          color: "text.secondary",
        }}
      >
        {label}
      </Typography>
      <Stack direction={"row"} spacing={1} alignItems="center">
        <Box sx={{ width: 90 }}>
          <Slider
            size="small"
            value={value}
            onChange={(e, newValue) => {
              onChange(Number(newValue));
            }}
            onChangeCommitted={
              onChangeCommitted
                ? (e, newValue) => onChangeCommitted(Number(newValue))
                : undefined
            }
            min={min}
            max={max}
            step={step}
          />
        </Box>
        <TextField
          value={step < 1 ? value.toFixed(2) : value}
          onChange={(e) => onChange(Number(e.target.value))}
          size="small"
          sx={{
            width: textFieldWidth,
            "& .MuiOutlinedInput-root": {
              height: "32px",
              "& input": {
                padding: "4px 4px",
                fontSize: "0.8rem",
              },
            },
          }}
        />
      </Stack>
    </Stack>
  );
};

export default SliderWithInput;
