import React from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Box,
  Typography,
  IconButton,
} from "@mui/material";
import KeyboardIcon from "@mui/icons-material/Keyboard";
import CloseIcon from "@mui/icons-material/Close";

// 快捷键类型定义
export interface ShortcutKey {
  key: string;
  description: string;
  category: string;
}

interface ShortcutsDialogProps {
  open: boolean;
  onClose: () => void;
  shortcuts: ShortcutKey[];
}

export const ShortcutsDialog: React.FC<ShortcutsDialogProps> = ({
  open,
  onClose,
  shortcuts,
}) => {
  // 获取所有不重复的类别
  const categories = Array.from(new Set(shortcuts.map((k) => k.category)));

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      sx={{
        "& .MuiDialog-paper": {
          borderRadius: 2,
          minWidth: 500,
        },
      }}
    >
      <DialogTitle
        sx={{
          borderBottom: "1px solid",
          borderColor: "divider",
          py: 2,
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <KeyboardIcon />
          <Typography variant="h6">键盘快捷键</Typography>
        </Box>
        <IconButton size="small" onClick={onClose}>
          <CloseIcon fontSize="small" />
        </IconButton>
      </DialogTitle>
      <DialogContent sx={{ py: 3 }}>
        <Box sx={{ display: "flex", flexWrap: "wrap", gap: 4 }}>
          {/* 按类别分组显示快捷键 */}
          {categories.map((category) => (
            <Box key={category} sx={{ minWidth: 200 }}>
              <Typography
                variant="subtitle2"
                sx={{ mb: 1, color: "primary.main", fontWeight: 600 }}
              >
                {category}
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
                {shortcuts
                  .filter((k) => k.category === category)
                  .map((shortcut, index) => (
                    <Box
                      key={index}
                      sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                      }}
                    >
                      <Typography variant="body2">
                        {shortcut.description}
                      </Typography>
                      <Box
                        sx={{
                          backgroundColor: "action.hover",
                          px: 1,
                          py: 0.5,
                          borderRadius: 1,
                          minWidth: 60,
                          textAlign: "center",
                          fontFamily: "monospace",
                          fontSize: "0.8rem",
                          fontWeight: "medium",
                          border: "1px solid",
                          borderColor: "divider",
                        }}
                      >
                        {shortcut.key}
                      </Box>
                    </Box>
                  ))}
              </Box>
            </Box>
          ))}
        </Box>
      </DialogContent>
    </Dialog>
  );
};
