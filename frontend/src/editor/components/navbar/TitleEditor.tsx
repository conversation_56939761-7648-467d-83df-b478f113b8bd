import React, { useCallback, useRef, useState, useEffect } from "react";
import { Box, TextField, Typography, IconButton } from "@mui/material";
import CheckIcon from "@mui/icons-material/Check";
import CloseIcon from "@mui/icons-material/Close";

const DEFAULT_TITLE = "Untitled video";

interface TitleEditorProps {
  initialTitle: string;
  onSaveTitle: (title: string) => void;
}

export const TitleEditor: React.FC<TitleEditorProps> = ({
  initialTitle,
  onSaveTitle,
}) => {
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [videoTitle, setVideoTitle] = useState(initialTitle || DEFAULT_TITLE);
  const titleInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (initialTitle && initialTitle !== videoTitle) {
      setVideoTitle(initialTitle);
    }
  }, [initialTitle]);

  const handleTitleClick = useCallback(() => {
    setIsEditingTitle(true);
    setTimeout(() => {
      titleInputRef.current?.focus();
      titleInputRef.current?.select();
    }, 0);
  }, []);

  const handleTitleChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newTitle = event.target.value;
      setVideoTitle(newTitle);
    },
    []
  );

  const saveTitle = useCallback(
    (title: string) => {
      const finalTitle = title.trim() === "" ? DEFAULT_TITLE : title;
      setVideoTitle(finalTitle);
      onSaveTitle(finalTitle);
    },
    [onSaveTitle]
  );

  const handleTitleBlur = useCallback(() => {
    setIsEditingTitle(false);
    saveTitle(videoTitle);
  }, [videoTitle, saveTitle]);

  const handleTitleKeyDown = useCallback(
    (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === "Enter") {
        setIsEditingTitle(false);
        saveTitle(videoTitle);
      } else if (event.key === "Escape") {
        // Cancel editing and revert to previous title
        setIsEditingTitle(false);
        setVideoTitle(initialTitle || DEFAULT_TITLE);
      }
    },
    [videoTitle, initialTitle, saveTitle]
  );

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
      }}
    >
      {isEditingTitle ? (
        <Box sx={{ display: "flex", alignItems: "center", minWidth: 80 }}>
          <TextField
            inputRef={titleInputRef}
            value={videoTitle}
            onChange={handleTitleChange}
            onBlur={handleTitleBlur}
            onKeyDown={handleTitleKeyDown}
            variant="outlined"
            size="small"
            autoFocus
            sx={{
              "& .MuiOutlinedInput-root": {
                borderRadius: 2,
              },
            }}
          />
          <Box sx={{ display: "flex", ml: 1 }}>
            <IconButton
              size="small"
              onClick={() => {
                saveTitle(videoTitle);
                setIsEditingTitle(false);
              }}
            >
              <CheckIcon fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={() => {
                setVideoTitle(initialTitle || DEFAULT_TITLE);
                setIsEditingTitle(false);
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>
      ) : (
        <Box
          sx={{
            display: "flex",
            alignItems: "center",
            border: "1px solid transparent",
            borderRadius: 2,
            px: 1.5,
            py: 0.5,
            "&:hover": {
              borderColor: "divider",
              backgroundColor: "action.hover",
            },
          }}
          onClick={handleTitleClick}
        >
          <Typography
            variant="subtitle1"
            sx={{
              cursor: "pointer",
              mr: 1,
            }}
          >
            {videoTitle}
          </Typography>
        </Box>
      )}
    </Box>
  );
};
