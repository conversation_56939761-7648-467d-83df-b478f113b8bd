import React from "react";
import { Box, IconButton, Tooltip } from "@mui/material";
import UndoIcon from "@mui/icons-material/Undo";
import RedoIcon from "@mui/icons-material/Redo";

interface HistoryButtonsProps {
  onUndo: () => void;
  onRedo: () => void;
  undoActionType?: string;
  redoActionType?: string;
  buttonStyle?: React.CSSProperties;
}

export const HistoryButtons: React.FC<HistoryButtonsProps> = ({
  onUndo,
  onRedo,
  undoActionType,
  redoActionType,
  buttonStyle,
}) => {
  const baseButtonStyle = {
    color: "text.secondary",
    "&:hover": {
      backgroundColor: "action.hover",
    },
    ...buttonStyle,
  };

  return (
    <Box
      sx={{
        display: "flex",
        alignItems: "center",
        gap: 0.5,
        height: "100%",
        px: 1,
      }}
    >
      <Tooltip
        title={`撤销 (Ctrl+Z)${undoActionType ? ` - ${undoActionType}` : ""}`}
        arrow
      >
        <span>
          <IconButton
            onClick={onUndo}
            size="small"
            disabled={!undoActionType}
            sx={baseButtonStyle}
          >
            <UndoIcon sx={{ fontSize: 20 }} />
          </IconButton>
        </span>
      </Tooltip>
      <Tooltip
        title={`重做 (Ctrl+Y)${redoActionType ? ` - ${redoActionType}` : ""}`}
        arrow
      >
        <span>
          <IconButton
            onClick={onRedo}
            size="small"
            disabled={!redoActionType}
            sx={baseButtonStyle}
          >
            <RedoIcon sx={{ fontSize: 20 }} />
          </IconButton>
        </span>
      </Tooltip>
    </Box>
  );
};
