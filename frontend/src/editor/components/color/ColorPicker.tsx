import FormatColorFillIcon from "@mui/icons-material/FormatColorFill";
import { Box, Popover } from "@mui/material";
import { useState } from "react";
import { SketchPicker } from "react-color";
interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
}

const ColorPicker: React.FC<ColorPickerProps> = ({ color, onChange }) => {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleChangeComplete = (color: { hex: string }) => {
    onChange(color.hex);
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <Box
        onClick={handleClick}
        sx={{
          m: 0.5,
          p: 0.5, // 添加内边距
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          transition: "all 0.2s ease",
          borderRadius: 1, // 添加圆角
          backgroundColor: "rgba(0,0,0,0.03)", // 添加背景色
          border: "1px solid rgba(0,0,0,0.08)", // 添加边框
          "&:hover": {
            boxShadow: "0 4px 8px rgba(0,0,0,0.1)", // 更柔和的阴影
            transform: "scale(1.03)", // 更细微的缩放
            backgroundColor: "rgba(0,0,0,0.06)", // hover时背景色变深
          },
        }}
      >
        <FormatColorFillIcon
          style={{
            color: color,
            filter: `drop-shadow(0 2px 2px rgba(0,0,0,0.1))`, // 图标添加阴影
          }}
          fontSize="small"
        />
      </Box>
      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        PaperProps={{
          elevation: 12, // 增加阴影
          sx: {
            borderRadius: 2,
            border: "1px solid rgba(0,0,0,0.08)", // 添加边框
            overflow: "hidden", // 确保内容不会溢出圆角
          },
        }}
      >
        <SketchPicker
          color={color}
          onChangeComplete={handleChangeComplete}
          disableAlpha={false}
        />
      </Popover>
    </>
  );
};

export default ColorPicker;
