import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import { Box, Button, Tab, Tabs, Typography } from "@mui/material";
import { useState } from "react";

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <Box
      role="tabpanel"
      hidden={value !== index}
      id={`upload-tabpanel-${index}`}
      aria-labelledby={`upload-tab-${index}`}
      {...other}
      sx={{ mt: 2 }}
    >
      {value === index && children}
    </Box>
  );
}

export const Uploads = () => {
  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };

  const handleAddImage = () => {};

  const handleAddAudio = () => {};

  return (
    <Box sx={{ flex: 1, borderRadius: 1 }}>
      <Box
        sx={{
          height: 48,
          display: "flex",
          alignItems: "center",
          px: 2,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1">Your media</Typography>
      </Box>
      <Box sx={{ p: 2 }}>
        <Box sx={{ borderRadius: 1 }}>
          <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
            <Tabs
              value={value}
              onChange={handleChange}
              variant="fullWidth"
              aria-label="upload tabs"
              sx={{
                "& .MuiTab-root": {
                  textTransform: "none",
                  fontWeight: 500,
                },
              }}
            >
              <Tab label="Project" />
              <Tab label="Workspace" />
            </Tabs>
          </Box>
          <TabPanel value={value} index={0}>
            <Button
              onClick={handleAddAudio}
              variant="contained"
              size="medium"
              fullWidth
              startIcon={<CloudUploadIcon />}
              sx={{
                textTransform: "none",
                bgcolor: "primary.main",
                color: "primary.contrastText",
                "&:hover": {
                  bgcolor: "primary.dark",
                },
                py: 1,
              }}
            >
              Upload Media
            </Button>
          </TabPanel>
          <TabPanel value={value} index={1}>
            <Button
              variant="contained"
              size="small"
              fullWidth
              startIcon={<CloudUploadIcon />}
              sx={{
                textTransform: "none",
                bgcolor: "action.selected",
                "&:hover": {
                  bgcolor: "action.selectedHover",
                },
              }}
            >
              Upload
            </Button>
            <Typography variant="body2" sx={{ mt: 2 }}>
              Some assets
            </Typography>
          </TabPanel>
        </Box>
      </Box>
    </Box>
  );
};
