import AddIcon from "@mui/icons-material/Add";
import ClearAllIcon from "@mui/icons-material/ClearAll";
import DeleteIcon from "@mui/icons-material/Delete";
import FileDownloadIcon from "@mui/icons-material/FileDownload";
import FileUploadIcon from "@mui/icons-material/FileUpload";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import {
  Box,
  Button,
  CircularProgress,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Paper,
  Snackbar,
  Alert,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import { observer } from "mobx-react";
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { StoreContext } from "../../store";
import { Caption } from "../../types";
import MergeIcon from "./MergeIcon";

// 常量样式定义
const styles = {
  iconButton: {
    opacity: 0.7,
    "&:hover": { opacity: 1 },
  },
  textFieldBase: {
    flex: 1,
    "& .MuiInputBase-input": {
      fontSize: "0.8rem",
      padding: "0",
    },
    "& .MuiOutlinedInput-root": {
      padding: "0",
      "& fieldset": {
        border: "none",
      },
    },
  },
  dashedLine: {
    flex: 1,
    borderBottom: "1px dashed",
    borderColor: "primary.main",
    opacity: 0.5,
  },
};

// 定义组件的Props类型
interface PlaySegmentButtonProps {
  caption: Caption;
  store: any;
}

interface DeleteCaptionButtonProps {
  caption: Caption;
  store: any;
}

interface CaptionSeparatorProps {
  index: number;
  store: any;
  hoveredIndex: number | null;
  setHoveredIndex: (index: number | null) => void;
}

interface SelectedCaptionItemProps {
  caption: Caption;
  store: any;
}

// 定义时间错误类型
type TimeError = {
  startTime: string | null;
  endTime: string | null;
};

interface UnselectedCaptionItemProps {
  caption: Caption;
  store: any;
  hoveredCaptionId: string | null;
}

interface CaptionItemProps {
  caption: Caption;
  store: any;
  selectedCaptionRef: React.RefObject<HTMLDivElement>;
  hoveredCaptionId: string | null;
  setHoveredCaptionId: (id: string | null) => void;
}

interface ClearConfirmDialogProps {
  isOpen: boolean;
  onCancel: () => void;
  onConfirm: () => void;
}

interface CaptionsHeaderProps {
  store: any;
  onClearAllClick: () => void;
  onUploadClick: () => void;
  onExportClick: () => void;
  isUploading: boolean;
  fileInputRef: React.RefObject<HTMLInputElement>;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

// 播放字幕片段组件
const PlaySegmentButton = React.memo(
  ({ caption, store }: PlaySegmentButtonProps) => {
    const handlePlaySegment = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        const startTimeMs = store.captionManager.timeStringToMilliseconds(
          caption.startTime
        );
        store.handleSeek(startTimeMs);
      },
      [caption.startTime, store]
    );

    return (
      <IconButton
        size="small"
        title="Play this segment"
        onClick={handlePlaySegment}
        sx={styles.iconButton}
      >
        <PlayArrowIcon fontSize="small" />
      </IconButton>
    );
  }
);

// 删除字幕按钮组件
const DeleteCaptionButton = React.memo(
  ({ caption, store }: DeleteCaptionButtonProps) => {
    const handleDeleteCaption = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        store.deleteCaption(caption.id);
      },
      [caption.id, store]
    );

    return (
      <IconButton
        size="small"
        onClick={handleDeleteCaption}
        title="Delete caption"
        sx={styles.iconButton}
      >
        <DeleteIcon fontSize="small" />
      </IconButton>
    );
  }
);

// 字幕分隔区域组件
const CaptionSeparator = React.memo(
  ({ index, store, hoveredIndex, setHoveredIndex }: CaptionSeparatorProps) => {
    if (index === 0) return null;

    return (
      <Box
        sx={{
          position: "relative",
          height: "20px",
          my: -1,
          zIndex: 1,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
        onMouseEnter={() => setHoveredIndex(index)}
        onMouseLeave={() => setHoveredIndex(null)}
      >
        {hoveredIndex === index && (
          <Box
            sx={{
              display: "flex",
              gap: 2,
              position: "absolute",
              width: "100%",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Box sx={styles.dashedLine} />
            <IconButton
              size="small"
              onClick={() => store.addCaptionBetween(index)}
              title="Insert caption"
            >
              <AddIcon fontSize="small" />
            </IconButton>
            <IconButton
              size="small"
              onClick={() => store.mergeCaptions(index)}
              title="Merge captions"
            >
              <MergeIcon fontSize="small" />
            </IconButton>
            <Box sx={styles.dashedLine} />
          </Box>
        )}
      </Box>
    );
  }
);

// 字幕项组件 - 选中状态
const SelectedCaptionItem = React.memo(
  ({ caption, store }: SelectedCaptionItemProps) => {
    // 状态管理
    const [timeErrors, setTimeErrors] = useState<TimeError>({
      startTime: null,
      endTime: null,
    });
    const [snackbarOpen, setSnackbarOpen] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState("");
    const [snackbarSeverity, setSnackbarSeverity] = useState<
      "success" | "warning" | "error" | "info"
    >("warning");

    // 获取所有字幕，用于检查时间重叠
    const allCaptions = useMemo(() => store.captions, [store.captions]);

    // 检查当前时间是否在字幕时间范围内并更新显示
    const checkAndUpdateCaption = useCallback(
      (formattedTime: string, field: string) => {
        const currentTimeMs = store.currentTimeInMs;
        const startTimeMs = store.captionManager.timeStringToMilliseconds(
          field === "startTime" ? formattedTime : caption.startTime
        );
        const endTimeMs = store.captionManager.timeStringToMilliseconds(
          field === "endTime" ? formattedTime : caption.endTime
        );

        if (currentTimeMs >= startTimeMs && currentTimeMs < endTimeMs) {
          store.captionManager.updateCurrentCaption(currentTimeMs);
        }
      },
      [caption.endTime, caption.startTime, store]
    );

    // 验证时间格式和逻辑
    const validateTime = useCallback(
      (value: string, field: "startTime" | "endTime"): string | null => {
        // 1. 检查格式
        const timeRegex = /^([0-9]{2}):([0-9]{2}):([0-9]{2})$/;
        if (!timeRegex.test(value)) {
          return "Format should be HH:MM:SS";
        }

        // 2. 检查时间值的有效性
        const [hours, minutes, seconds] = value.split(":").map(Number);
        if (hours > 23 || minutes > 59 || seconds > 59) {
          return "Invalid time value";
        }

        // 3. 检查开始时间和结束时间的逻辑关系
        const startTimeMs = store.captionManager.timeStringToMilliseconds(
          field === "startTime" ? value : caption.startTime
        );
        const endTimeMs = store.captionManager.timeStringToMilliseconds(
          field === "endTime" ? value : caption.endTime
        );

        if (field === "startTime" && startTimeMs >= endTimeMs) {
          return "Start time must be less than end time";
        }

        if (field === "endTime" && endTimeMs <= startTimeMs) {
          return "End time must be greater than start time";
        }

        // 4. 检查与其他字幕的时间重叠
        const otherCaptions = allCaptions.filter(
          (c: Caption) => c.id !== caption.id
        );
        for (const otherCaption of otherCaptions) {
          const otherStartMs = store.captionManager.timeStringToMilliseconds(
            otherCaption.startTime
          );
          const otherEndMs = store.captionManager.timeStringToMilliseconds(
            otherCaption.endTime
          );

          // 检查是否有重叠
          const thisStartMs =
            field === "startTime"
              ? startTimeMs
              : store.captionManager.timeStringToMilliseconds(
                  caption.startTime
                );
          const thisEndMs =
            field === "endTime"
              ? endTimeMs
              : store.captionManager.timeStringToMilliseconds(caption.endTime);

          if (thisStartMs < otherEndMs && thisEndMs > otherStartMs) {
            return `Time overlaps with adjacent captions`;
          }
        }

        return null;
      },
      [allCaptions, caption.id, caption.startTime, caption.endTime, store]
    );

    // 处理时间输入，确保只能输入有效的时间格式
    const handleTimeInput = useCallback(
      (value: string, field: "startTime" | "endTime"): string => {
        // 允许空字符串
        if (value === "") return "";

        // 获取当前字段的值
        const currentValue =
          field === "startTime" ? caption.startTime : caption.endTime;

        // 只允许数字和冒号
        const cleanValue = value.replace(/[^0-9:]/g, "");

        // 处理冒号的自动添加
        let formattedValue = cleanValue;

        // 如果用户输入了2位数字，自动添加冒号
        if (
          cleanValue.length === 2 &&
          !cleanValue.includes(":") &&
          currentValue.length < 2
        ) {
          formattedValue = cleanValue + ":";
        }

        // 如果用户输入了5位数字和冒号，自动添加第二个冒号
        if (
          cleanValue.length === 5 &&
          cleanValue.indexOf(":") === 2 &&
          !cleanValue.includes(":", 3) &&
          currentValue.length < 5
        ) {
          formattedValue = cleanValue + ":";
        }

        // 限制最大长度为8（HH:MM:SS格式）
        if (formattedValue.length > 8) {
          formattedValue = formattedValue.substring(0, 8);
        }

        // 如果不是完整的时间格式，允许继续输入
        if (formattedValue.length < 8) {
          setTimeErrors((prev) => ({ ...prev, [field]: null }));
          return formattedValue;
        }

        // 验证时间格式
        const timeRegex = /^([0-9]{2}):([0-9]{2}):([0-9]{2})$/;
        if (!timeRegex.test(formattedValue)) {
          setTimeErrors((prev) => ({
            ...prev,
            [field]: "格式应为 HH:MM:SS",
          }));
          // 返回当前值，不更新字幕
          return currentValue;
        }

        // 验证时间值的有效性
        const [hours, minutes, seconds] = formattedValue.split(":").map(Number);
        if (hours > 23 || minutes > 59 || seconds > 59) {
          setTimeErrors((prev) => ({ ...prev, [field]: "时间值无效" }));
          // 返回当前值，不更新字幕
          return currentValue;
        }

        // 验证时间关系和重叠
        const error = validateTime(formattedValue, field);
        setTimeErrors((prev) => ({ ...prev, [field]: error }));

        if (error) {
          setSnackbarMessage(error);
          setSnackbarSeverity("warning");
          setSnackbarOpen(true);
          // 返回当前值，不更新字幕
          return currentValue;
        }

        // 验证通过，返回格式化后的值
        return formattedValue;
      },
      [caption.startTime, caption.endTime, validateTime]
    );

    // 处理开始时间变化
    const handleStartTimeChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        const formattedValue = handleTimeInput(value, "startTime");
        store.updateCaption(caption.id, "startTime", formattedValue);
      },
      [caption.id, store, handleTimeInput]
    );

    // 处理开始时间失去焦点
    const handleStartTimeBlur = useCallback(
      (e: React.FocusEvent<HTMLInputElement>) => {
        const value = e.target.value;

        // 如果输入为空，恢复为原始值
        if (value === "") {
          store.updateCaption(caption.id, "startTime", caption.startTime);
          setTimeErrors((prev) => ({ ...prev, startTime: null }));
          return;
        }

        // 如果有错误，提供建议
        if (timeErrors.startTime) {
          // 获取建议的时间值
          let suggestedTime = "";

          if (
            timeErrors.startTime.includes("时间重叠") ||
            timeErrors.startTime.includes("开始时间必须小于结束时间")
          ) {
            // 计算一个合适的开始时间（结束时间减去3秒）
            const endTimeMs = store.captionManager.timeStringToMilliseconds(
              caption.endTime
            );
            const newStartTimeMs = Math.max(0, endTimeMs - 3000); // 至少3秒的持续时间

            // 转换回时间字符串格式
            const hours = Math.floor(newStartTimeMs / 3600000);
            const minutes = Math.floor((newStartTimeMs % 3600000) / 60000);
            const seconds = Math.floor((newStartTimeMs % 60000) / 1000);
            suggestedTime = `${hours.toString().padStart(2, "0")}:${minutes
              .toString()
              .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

            setSnackbarMessage(`建议使用 ${suggestedTime} 作为开始时间`);
            setSnackbarSeverity("info");
            setSnackbarOpen(true);
          }

          // 注意：我们不恢复原始值，因为handleTimeInput已经确保了不会保存无效的时间
          // 输入框会显示用户输入的值，但字幕对象中的值仍然是有效的原始值
        } else if (value.length === 8) {
          // 如果没有错误且是完整的时间格式，格式化并更新
          const formattedTime = store.formatTimeString(value);
          store.updateCaption(caption.id, "startTime", formattedTime);
          checkAndUpdateCaption(formattedTime, "startTime");
        }
        // 如果不是完整的时间格式且没有错误，保持当前输入状态
      },
      [
        caption.id,
        caption.endTime,
        caption.startTime,
        checkAndUpdateCaption,
        store,
        timeErrors.startTime,
      ]
    );

    // 处理结束时间变化
    const handleEndTimeChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        const formattedValue = handleTimeInput(value, "endTime");
        store.updateCaption(caption.id, "endTime", formattedValue);
      },
      [caption.id, store, handleTimeInput]
    );

    // 处理结束时间失去焦点
    const handleEndTimeBlur = useCallback(
      (e: React.FocusEvent<HTMLInputElement>) => {
        const value = e.target.value;

        // 如果输入为空，恢复为原始值
        if (value === "") {
          store.updateCaption(caption.id, "endTime", caption.endTime);
          setTimeErrors((prev) => ({ ...prev, endTime: null }));
          return;
        }

        // 如果有错误，提供建议
        if (timeErrors.endTime) {
          // 获取建议的时间值
          let suggestedTime = "";

          if (
            timeErrors.endTime.includes("时间重叠") ||
            timeErrors.endTime.includes("结束时间必须大于开始时间")
          ) {
            // 计算一个合适的结束时间（开始时间加上3秒）
            const startTimeMs = store.captionManager.timeStringToMilliseconds(
              caption.startTime
            );
            const newEndTimeMs = startTimeMs + 3000; // 至少3秒的持续时间

            // 转换回时间字符串格式
            const hours = Math.floor(newEndTimeMs / 3600000);
            const minutes = Math.floor((newEndTimeMs % 3600000) / 60000);
            const seconds = Math.floor((newEndTimeMs % 60000) / 1000);
            suggestedTime = `${hours.toString().padStart(2, "0")}:${minutes
              .toString()
              .padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;

            setSnackbarMessage(`建议使用 ${suggestedTime} 作为结束时间`);
            setSnackbarSeverity("info");
            setSnackbarOpen(true);
          }

          // 注意：我们不恢复原始值，因为handleTimeInput已经确保了不会保存无效的时间
          // 输入框会显示用户输入的值，但字幕对象中的值仍然是有效的原始值
        } else if (value.length === 8) {
          // 如果没有错误且是完整的时间格式，格式化并更新
          const formattedTime = store.formatTimeString(value);
          store.updateCaption(caption.id, "endTime", formattedTime);
          checkAndUpdateCaption(formattedTime, "endTime");
        }
        // 如果不是完整的时间格式且没有错误，保持当前输入状态
      },
      [
        caption.id,
        caption.startTime,
        caption.endTime,
        checkAndUpdateCaption,
        store,
        timeErrors.endTime,
      ]
    );

    // 处理文本变化
    const handleTextChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        store.updateCaption(caption.id, "text", e.target.value);

        const currentTimeMs = store.currentTimeInMs;
        const startTimeMs = store.captionManager.timeStringToMilliseconds(
          caption.startTime
        );
        const endTimeMs = store.captionManager.timeStringToMilliseconds(
          caption.endTime
        );

        if (currentTimeMs >= startTimeMs && currentTimeMs < endTimeMs) {
          store.captionManager.updateCurrentCaption(currentTimeMs);
        }
      },
      [caption.endTime, caption.id, caption.startTime, store]
    );

    // 关闭提示信息
    const handleSnackbarClose = useCallback(() => {
      setSnackbarOpen(false);
    }, []);

    return (
      <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
          }}
        >
          <Box sx={{ display: "flex", gap: 0, flex: 1 }}>
            <TextField
              size="small"
              variant="outlined"
              value={caption.startTime}
              onChange={handleStartTimeChange}
              onBlur={handleStartTimeBlur}
              placeholder="00:00:00"
              error={!!timeErrors.startTime}
              sx={{
                ...styles.textFieldBase,
                "& .MuiOutlinedInput-root": {
                  ...styles.textFieldBase["& .MuiOutlinedInput-root"],
                  "& fieldset": {
                    ...styles.textFieldBase["& .MuiOutlinedInput-root"][
                      "& fieldset"
                    ],
                    borderColor: timeErrors.startTime
                      ? "error.main"
                      : "transparent",
                  },
                  "&:hover fieldset": {
                    borderColor: timeErrors.startTime
                      ? "error.main"
                      : "divider",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: timeErrors.startTime
                      ? "error.main"
                      : "primary.main",
                  },
                },
              }}
            />
            <TextField
              size="small"
              variant="outlined"
              value={caption.endTime}
              onChange={handleEndTimeChange}
              onBlur={handleEndTimeBlur}
              placeholder="00:00:00"
              error={!!timeErrors.endTime}
              sx={{
                ...styles.textFieldBase,
                "& .MuiOutlinedInput-root": {
                  ...styles.textFieldBase["& .MuiOutlinedInput-root"],
                  "& fieldset": {
                    ...styles.textFieldBase["& .MuiOutlinedInput-root"][
                      "& fieldset"
                    ],
                    borderColor: timeErrors.endTime
                      ? "error.main"
                      : "transparent",
                  },
                  "&:hover fieldset": {
                    borderColor: timeErrors.endTime ? "error.main" : "divider",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: timeErrors.endTime
                      ? "error.main"
                      : "primary.main",
                  },
                },
              }}
            />
          </Box>
          <Box sx={{ display: "flex", ml: 1 }}>
            <PlaySegmentButton caption={caption} store={store} />
            <DeleteCaptionButton caption={caption} store={store} />
          </Box>
        </Box>
        <TextField
          fullWidth
          size="small"
          multiline
          value={caption.text}
          onChange={handleTextChange}
          sx={styles.textFieldBase}
        />

        {/* 错误提示 */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={3000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert
            onClose={handleSnackbarClose}
            severity={snackbarSeverity}
            variant="filled"
            sx={{ width: "100%" }}
          >
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </Box>
    );
  }
);

// 字幕项组件 - 未选中状态
const UnselectedCaptionItem = React.memo(
  ({ caption, store, hoveredCaptionId }: UnselectedCaptionItemProps) => {
    return (
      <Box>
        <Typography
          sx={{
            color: "text.secondary",
            fontSize: "0.8rem",
            mb: 0.5,
          }}
        >
          {caption.startTime} - {caption.endTime}
        </Typography>
        {(caption.text || "No text")
          .split("\n")
          .map((line: string, i: number) => (
            <Typography
              key={i}
              variant="body2"
              sx={{
                color: "text.primary",
                fontWeight: caption.text ? "normal" : "light",
                fontStyle: caption.text ? "normal" : "italic",
              }}
            >
              {line || " "}
            </Typography>
          ))}

        {hoveredCaptionId === caption.id && (
          <Box
            sx={{
              position: "absolute",
              top: 2,
              right: 2,
              display: "flex",
            }}
          >
            <PlaySegmentButton caption={caption} store={store} />
            <DeleteCaptionButton caption={caption} store={store} />
          </Box>
        )}
      </Box>
    );
  }
);

// 字幕项组件
const CaptionItem = React.memo(
  ({
    caption,
    store,
    selectedCaptionRef,
    hoveredCaptionId,
    setHoveredCaptionId,
  }: CaptionItemProps) => {
    const handleCaptionClick = useCallback(
      (e: React.MouseEvent) => {
        e.stopPropagation();
        store.selectCaption(caption.id);
      },
      [caption.id, store]
    );

    return (
      <Paper
        ref={caption.isSelected ? selectedCaptionRef : undefined}
        onClick={handleCaptionClick}
        elevation={1}
        onMouseEnter={() => setHoveredCaptionId(caption.id)}
        onMouseLeave={() => setHoveredCaptionId(null)}
        sx={{
          my: 2,
          p: 1,
          borderRadius: 1,
          bgcolor: caption.isSelected ? "grey.300" : "action.selected",
          borderColor: caption.isSelected ? "primary.main" : "divider",
          "&:hover": {
            bgcolor: caption.isSelected ? "grey.300" : "action.hover",
          },
          cursor: "pointer",
          position: "relative",
        }}
      >
        {caption.isSelected ? (
          <SelectedCaptionItem caption={caption} store={store} />
        ) : (
          <UnselectedCaptionItem
            caption={caption}
            store={store}
            hoveredCaptionId={hoveredCaptionId}
          />
        )}
      </Paper>
    );
  }
);

// 确认对话框组件
const ClearConfirmDialog = React.memo(
  ({ isOpen, onCancel, onConfirm }: ClearConfirmDialogProps) => {
    return (
      <Dialog
        open={isOpen}
        onClose={onCancel}
        aria-labelledby="clear-captions-dialog-title"
        aria-describedby="clear-captions-dialog-description"
        slotProps={{
          paper: {
            sx: {
              borderRadius: 2,
              minWidth: 360,
            },
          },
        }}
      >
        <DialogTitle
          id="clear-captions-dialog-title"
          sx={{
            pb: 1,
            fontWeight: 600,
          }}
        >
          Clear All Captions
        </DialogTitle>
        <DialogContent sx={{ pb: 2 }}>
          <DialogContentText
            id="clear-captions-dialog-description"
            sx={{
              color: "text.primary",
            }}
          >
            Are you sure you want to delete all captions? This action cannot be
            undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button
            onClick={onCancel}
            variant="outlined"
            sx={{
              borderRadius: 1.5,
              textTransform: "none",
              px: 3,
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            color="error"
            variant="contained"
            autoFocus
            sx={{
              borderRadius: 1.5,
              textTransform: "none",
              px: 3,
            }}
          >
            Clear All
          </Button>
        </DialogActions>
      </Dialog>
    );
  }
);

// 头部工具栏组件
const CaptionsHeader = React.memo(
  ({
    store,
    onClearAllClick,
    onUploadClick,
    onExportClick,
    isUploading,
    fileInputRef,
    handleFileChange,
  }: CaptionsHeaderProps) => {
    return (
      <Box
        sx={{
          height: 56,
          display: "flex",
          alignItems: "center",
          px: 3,
          flexShrink: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
          justifyContent: "space-between",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "bold" }}>
          Captions
        </Typography>
        <Box sx={{ display: "flex" }}>
          <Tooltip title="Clear all captions">
            <IconButton
              onClick={onClearAllClick}
              size="small"
              sx={{ mr: 1 }}
              disabled={store.captions.length === 0}
            >
              <ClearAllIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Upload SRT file">
            <IconButton
              onClick={onUploadClick}
              size="small"
              sx={{
                mr: 1,
                position: "relative",
                color: isUploading ? "primary.main" : "inherit",
              }}
              disabled={isUploading}
            >
              {isUploading ? (
                <CircularProgress
                  size={16}
                  thickness={5}
                  sx={{ position: "absolute" }}
                />
              ) : null}
              <FileUploadIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Export captions">
            <IconButton
              onClick={onExportClick}
              size="small"
              sx={{ mr: 2 }}
              disabled={store.captions.length === 0}
            >
              <FileDownloadIcon fontSize="small" />
            </IconButton>
          </Tooltip>
          <input
            type="file"
            ref={fileInputRef}
            accept=".srt"
            style={{ display: "none" }}
            onChange={handleFileChange}
          />
        </Box>
      </Box>
    );
  }
);

const Captions: React.FC = observer(() => {
  const store = React.useContext(StoreContext);
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [hoveredCaptionId, setHoveredCaptionId] = useState<string | null>(null);
  const captionsContainerRef = useRef<HTMLDivElement>(null);
  const selectedCaptionRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);

  // 处理点击字幕外部区域
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectedCaptionRef.current &&
        !selectedCaptionRef.current.contains(event.target as Node) &&
        !event.defaultPrevented
      ) {
        store.deselectAllCaptions();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [store]);

  // 滚动到选中的字幕
  useEffect(() => {
    const selectedCaption = store.captions.find(
      (caption) => caption.isSelected
    );

    if (
      selectedCaption &&
      selectedCaptionRef.current &&
      captionsContainerRef.current
    ) {
      // 使用requestAnimationFrame确保DOM已更新
      requestAnimationFrame(() => {
        if (!selectedCaptionRef.current || !captionsContainerRef.current)
          return;

        const containerRect =
          captionsContainerRef.current.getBoundingClientRect();
        const selectedRect = selectedCaptionRef.current.getBoundingClientRect();

        // 计算相对位置
        const topRelative = selectedRect.top - containerRect.top;
        const bottomRelative = selectedRect.bottom - containerRect.top;
        const containerHeight = containerRect.height;

        // 检查是否需要滚动
        if (topRelative < 0 || bottomRelative > containerHeight) {
          // 计算滚动位置，使选中元素居中
          const scrollTop =
            captionsContainerRef.current.scrollTop +
            topRelative -
            containerHeight / 2 +
            selectedRect.height / 2;

          captionsContainerRef.current.scrollTo({
            top: scrollTop,
            behavior: "smooth",
          });
        }
      });
    }
  }, [store.captions]);

  // 添加字幕
  const handleAddCaption = useCallback(() => {
    store.addCaption();

    // 滚动到新添加的字幕
    requestAnimationFrame(() => {
      if (captionsContainerRef.current) {
        captionsContainerRef.current.scrollTop =
          captionsContainerRef.current.scrollHeight;
      }
    });
  }, [store]);

  // 导出SRT
  const handleExportSRT = useCallback(() => {
    store.exportAndDownloadCaptionsAsSRT();
  }, [store]);

  // 点击上传按钮
  const handleUploadClick = useCallback(() => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  }, []);

  // 处理文件上传
  const handleFileChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (!file) return;

      // 重置input，以便可以再次上传相同文件
      e.target.value = "";

      setIsUploading(true);

      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        if (content) {
          try {
            store.importCaptionsFromSRT(content);

            // 滚动到顶部显示导入的字幕
            if (captionsContainerRef.current) {
              captionsContainerRef.current.scrollTop = 0;
            }
          } catch (error) {
            console.error("Error importing captions:", error);
          } finally {
            setIsUploading(false);
          }
        }
      };

      reader.onerror = () => {
        console.error("Error reading file");
        setIsUploading(false);
      };

      reader.readAsText(file);
    },
    [store]
  );

  // 清除所有字幕
  const handleClearAllClick = useCallback(() => {
    setIsConfirmDialogOpen(true);
  }, []);

  // 确认清除所有字幕
  const handleClearConfirm = useCallback(() => {
    store.clearAllCaptions();
    setIsConfirmDialogOpen(false);
  }, [store]);

  // 取消清除所有字幕
  const handleClearCancel = useCallback(() => {
    setIsConfirmDialogOpen(false);
  }, []);

  // 渲染字幕项
  const renderCaptionItem = useCallback(
    (caption: Caption, index: number) => {
      return (
        <React.Fragment key={caption.id}>
          {index > 0 && (
            <CaptionSeparator
              index={index}
              store={store}
              hoveredIndex={hoveredIndex}
              setHoveredIndex={setHoveredIndex}
            />
          )}
          <CaptionItem
            caption={caption}
            store={store}
            selectedCaptionRef={selectedCaptionRef}
            hoveredCaptionId={hoveredCaptionId}
            setHoveredCaptionId={setHoveredCaptionId}
          />
        </React.Fragment>
      );
    },
    [hoveredCaptionId, hoveredIndex, store]
  );

  // 使用useMemo优化字幕列表渲染
  const captionsList = useMemo(() => {
    return store.captions?.map((caption, index) =>
      renderCaptionItem(caption, index)
    );
  }, [renderCaptionItem, store.captions]);

  return (
    <Box
      sx={{
        height: "100%",
        width: "100%",
        display: "flex",
        flexDirection: "column",
        bgcolor: "grey.100",
        borderRadius: 1,
      }}
    >
      <CaptionsHeader
        store={store}
        onClearAllClick={handleClearAllClick}
        onUploadClick={handleUploadClick}
        onExportClick={handleExportSRT}
        isUploading={isUploading}
        fileInputRef={fileInputRef}
        handleFileChange={handleFileChange}
      />

      <Box
        ref={captionsContainerRef}
        sx={{ flexGrow: 1, overflow: "auto", px: 2 }}
      >
        {captionsList}
      </Box>

      <Box sx={{ p: 2, borderTop: 1, borderColor: "divider" }}>
        <Button
          fullWidth
          size="small"
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleAddCaption}
          sx={{
            textTransform: "none",
            py: 1.5,
            borderRadius: 2,
          }}
        >
          Add Caption
        </Button>
      </Box>

      <ClearConfirmDialog
        isOpen={isConfirmDialogOpen}
        onCancel={handleClearCancel}
        onConfirm={handleClearConfirm}
      />
    </Box>
  );
});

export default Captions;
