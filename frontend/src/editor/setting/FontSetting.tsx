import FormatAlignCenterIcon from "@mui/icons-material/FormatAlignCenter";
import FormatAlignLeftIcon from "@mui/icons-material/FormatAlignLeft";
import FormatAlignRightIcon from "@mui/icons-material/FormatAlignRight";
import FormatBoldIcon from "@mui/icons-material/FormatBold";
import FormatItalicIcon from "@mui/icons-material/FormatItalic";
import FormatUnderlinedIcon from "@mui/icons-material/FormatUnderlined";
import StrikethroughSIcon from "@mui/icons-material/StrikethroughS";
import {
  Box,
  Checkbox,
  Divider,
  FormControlLabel,
  MenuItem,
  Select,
  Slider,
  Stack,
  Tab,
  Tabs,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";
import { observer } from "mobx-react";
import React, { useState } from "react";
import { StoreContext } from "../../store";
import ColorPicker from "../components/color/ColorPicker";
import { GradientPicker } from "../components/color/GradientPicker"; // 假设我们创建了这个新组件
import BaseSetting from "./BaseSetting";
import { styled } from "@mui/system";

const FontSetting = observer(({ element }) => {
  const store = React.useContext(StoreContext);

  const [fontSize, setFontSize] = useState(
    element?.properties?.fontSize || 100
  );
  const [alignment, setAlignment] = useState(
    element?.properties?.textAlign || "left"
  );
  const [styles, setStyles] = useState(element?.properties?.styles || []);
  const [charSpace, setCharSpace] = useState(
    element?.properties?.charSpacing || 0
  );
  const [lineHeight, setLineHeight] = useState(
    element?.properties?.lineHeight || 0.3
  );
  const [fontColor, setFontColor] = useState(
    element?.properties?.fontColor || "#ffffff"
  );
  const [fontFamily, setFontFamily] = useState(
    element?.properties?.fontFamily || "Arial"
  );
  const [strokeWidth, setStrokeWidth] = useState(
    element?.properties?.strokeWidth || 0
  );
  const [strokeColor, setStrokeColor] = useState(
    element?.properties?.strokeColor || "#000000"
  );
  const [shadowColor, setShadowColor] = useState(
    element?.properties?.shadowColor || "#000000"
  );
  const [shadowBlur, setShadowBlur] = useState(
    element?.properties?.shadowBlur || 0
  );
  const [shadowOffsetX, setShadowOffsetX] = useState(
    element?.properties?.shadowOffsetX || 0
  );
  const [shadowOffsetY, setShadowOffsetY] = useState(
    element?.properties?.shadowOffsetY || 0
  );
  const [gradientColors, setGradientColors] = useState(
    element?.properties?.gradientColors || ["#ffffff", "#000000"]
  );
  const [useGradient, setUseGradient] = useState(
    element?.properties?.useGradient || false
  );

  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  const handleDelete = () => {
    store.deleteElement(element.id);
  };

  React.useEffect(() => {
    return () => {};
  }, []);

  const handleAlignmentChange = (event, newAlignment) => {
    if (newAlignment !== null) {
      setAlignment(newAlignment);
      store.updateTextStyle(element.id, { textAlign: newAlignment });
    }
  };

  const handleStyleChange = (event, newStyles) => {
    setStyles(newStyles);
    store.updateTextStyle(element.id, { styles: newStyles });
  };

  const handleColorChange = (color) => {
    if (useGradient) {
      // 如果使用渐变，更新渐变色
      const newGradientColors = [...gradientColors];
      newGradientColors[0] = color;
      setGradientColors(newGradientColors);
      store.updateTextStyle(element.id, {
        gradientColors: newGradientColors,
        useGradient: true,
      });
    } else {
      // 如果不使用渐变，更新普通颜色
      setFontColor(color);
      store.updateTextStyle(element.id, {
        fontColor: color,
        useGradient: false,
      });
    }
  };

  const handleGradientChange = (newGradientColors) => {
    setGradientColors(newGradientColors);
    store.updateTextStyle(element.id, {
      gradientColors: newGradientColors,
      useGradient: true,
    });
  };

  const handleFontFamilyChange = (event) => {
    const newFontFamily = event.target.value as string;
    setFontFamily(newFontFamily);
    store.updateTextStyle(element.id, { fontFamily: newFontFamily });
  };

  const fontFamilies = [
    "Arial",
    "Helvetica",
    "Times New Roman",
    "Courier",
    "Verdana",
    "Georgia",
    "Palatino",
    "Garamond",
    "Bookman",
    "Comic Sans MS",
    "Trebuchet MS",
    "Arial Black",
    "Impact",
    // Added new fonts
    "Roboto",
  ];

  const StyledSelect = styled(Select)({
    "& .MuiSelect-select": {
      fontSize: "0.9rem",
    },
  });

  const StyledMenuItem = styled(MenuItem)({
    fontSize: "0.9rem",
    display: "flex",
    alignItems: "center",
    "&:hover": {
      backgroundColor: "#f0f0f0",
    },
  });

  const FontPreview = styled("span")({
    marginRight: "8px",
    fontSize: "0.9rem",
  });

  return (
    <Box
      sx={{
        height: "100vh",
        display: "flex",
        flexDirection: "column",
        p: 2,
        mx: 1,
        mb: 2,
        flexGrow: 1,
        overflowY: "auto",
      }}
    >
      <Typography variant="h6" sx={{ mb: 1, py: 1 }}>
        Font Setting
      </Typography>
      <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
        <Tab label="Basic" />
        <Tab label="Advanced" />
      </Tabs>
      <Stack spacing={2}>
        {activeTab === 0 && (
          <>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body2">Font Family</Typography>
              <StyledSelect
                value={fontFamily}
                onChange={handleFontFamilyChange}
                sx={{ width: "50%" }}
                size="small"
              >
                {fontFamilies.map((font) => (
                  <StyledMenuItem key={font} value={font}>
                    <FontPreview style={{ fontFamily: font }}>
                      {font}
                    </FontPreview>
                  </StyledMenuItem>
                ))}
              </StyledSelect>
            </Stack>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              spacing={0.5}
            >
              <Typography variant="body2">Font Size</Typography>
              <Box sx={{ width: "50%" }}>
                <Slider
                  size="small"
                  value={fontSize}
                  onChange={(e, newValue) => {
                    setFontSize(Number(newValue));
                  }}
                  onChangeCommitted={(e, newValue) => {
                    setFontSize(Number(newValue));
                    store.updateTextStyle(element.id, {
                      fontSize: Number(newValue),
                    });
                  }}
                  min={0}
                  max={200}
                  sx={{ color: "#2196f3" }}
                />
              </Box>
              <TextField
                value={fontSize}
                size="small"
                onChange={(e) => setFontSize(Number(e.target.value))}
                variant="outlined"
                sx={{ width: "50px" }}
              />
            </Stack>

            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2">Color</Typography>
              {useGradient ? (
                <GradientPicker
                  colors={gradientColors}
                  onChange={handleGradientChange}
                />
              ) : (
                <ColorPicker color={fontColor} onChange={handleColorChange} />
              )}
              <FormControlLabel
                control={
                  <Checkbox
                    size="small"
                    checked={useGradient}
                    onChange={(e) => {
                      setUseGradient(e.target.checked);
                      if (e.target.checked)
                        store.updateTextStyle(element.id, {
                          gradientColors: gradientColors,
                          useGradient: true,
                        });
                      else
                        store.updateTextStyle(element.id, {
                          fontColor: fontColor,
                          useGradient: true,
                        });
                    }}
                  />
                }
                label={<Typography variant="body2">Gradient</Typography>}
              />
            </Stack>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              spacing={0.5}
            >
              <Typography variant="body2">Char Space</Typography>
              <Box sx={{ width: "40%" }}>
                <Slider
                  size="small"
                  value={charSpace}
                  onChange={(e, newValue) => {
                    setCharSpace(Number(newValue));
                  }}
                  onChangeCommitted={(e, newValue) => {
                    setCharSpace(Number(newValue));
                    store.updateTextStyle(element.id, {
                      charSpacing: Number(newValue),
                    });
                  }}
                  min={-200}
                  max={800}
                  step={0.1}
                  sx={{ color: "#2196f3" }}
                />
              </Box>
              <TextField
                value={charSpace}
                onChange={(e) => setCharSpace(Number(e.target.value))}
                variant="outlined"
                size="small"
                sx={{ width: "50px" }}
              />
            </Stack>

            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
              spacing={0.5}
            >
              <Typography variant="body2">Line Height</Typography>
              <Box sx={{ width: "40%" }}>
                <Slider
                  size="small"
                  value={lineHeight}
                  onChange={(e, newValue) => {
                    setLineHeight(Number(newValue));
                  }}
                  onChangeCommitted={(e, newValue) => {
                    setLineHeight(Number(newValue));
                    store.updateTextStyle(element.id, {
                      lineHeight: Number(newValue),
                    });
                  }}
                  min={0.5}
                  max={3}
                  step={0.01}
                  sx={{ color: "#2196f3" }}
                />
              </Box>
              <TextField
                value={lineHeight.toFixed(2)}
                onChange={(e) => setLineHeight(Number(e.target.value))}
                variant="outlined"
                size="small"
                sx={{ width: "50px" }}
              />
            </Stack>

            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body2">Alignment</Typography>
              <ToggleButtonGroup
                value={alignment}
                exclusive
                onChange={handleAlignmentChange}
                aria-label="text alignment"
                size="small"
              >
                <ToggleButton value="left" aria-label="left aligned">
                  <FormatAlignLeftIcon fontSize="small" />
                </ToggleButton>
                <ToggleButton value="center" aria-label="centered">
                  <FormatAlignCenterIcon fontSize="small" />
                </ToggleButton>
                <ToggleButton value="right" aria-label="right aligned">
                  <FormatAlignRightIcon fontSize="small" />
                </ToggleButton>
              </ToggleButtonGroup>
            </Stack>
            <Divider />
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography variant="body2">Style</Typography>
              <ToggleButtonGroup
                value={styles}
                onChange={handleStyleChange}
                aria-label="text styling"
                size="small"
              >
                <ToggleButton value="bold" aria-label="bold" size="small">
                  <FormatBoldIcon fontSize="small" />
                </ToggleButton>
                <ToggleButton value="italic" aria-label="italic" size="small">
                  <FormatItalicIcon fontSize="small" />
                </ToggleButton>
                <ToggleButton
                  value="underlined"
                  aria-label="underlined"
                  size="small"
                >
                  <FormatUnderlinedIcon fontSize="small" />
                </ToggleButton>
                <ToggleButton
                  value="strikethrough"
                  aria-label="strikethrough"
                  size="small"
                >
                  <StrikethroughSIcon fontSize="small" />
                </ToggleButton>
              </ToggleButtonGroup>
            </Stack>
            <Divider />
            <BaseSetting element={element} />
          </>
        )}
        {activeTab === 1 && (
          <>
            <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: "bold" }}>
              Stroke
            </Typography>
            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2">Stroke Width</Typography>
              <Slider
                size="small"
                value={strokeWidth}
                onChange={(e, newValue) => {
                  setStrokeWidth(Number(newValue));
                }}
                onChangeCommitted={(e, newValue) => {
                  setStrokeWidth(Number(newValue));
                  store.updateTextStyle(element.id, {
                    strokeWidth: Number(newValue),
                  });
                }}
                min={0}
                max={2}
                step={0.1}
                sx={{ width: "50%" }}
              />
              <TextField
                value={strokeWidth}
                onChange={(e) => {
                  const newValue = Number(e.target.value);
                  setStrokeWidth(newValue);
                  store.updateTextStyle(element.id, { strokeWidth: newValue });
                }}
                variant="outlined"
                size="small"
                sx={{ width: "60px" }}
              />
            </Stack>

            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2">Stroke Color</Typography>
              <ColorPicker
                color={strokeColor}
                onChange={(color) => {
                  setStrokeColor(color);
                  store.updateTextStyle(element.id, { strokeColor: color });
                }}
              />
            </Stack>

            <Divider />
            <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: "bold" }}>
              Shadow
            </Typography>
            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2">Color</Typography>
              <ColorPicker
                color={shadowColor}
                onChange={(color) => {
                  setShadowColor(color);
                  store.updateTextStyle(element.id, { shadowColor: color });
                }}
              />
            </Stack>

            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2">Blur</Typography>
              <Slider
                size="small"
                value={shadowBlur}
                onChange={(e, newValue) => {
                  setShadowBlur(Number(newValue));
                }}
                onChangeCommitted={(e, newValue) => {
                  setShadowBlur(Number(newValue));
                  store.updateTextStyle(element.id, {
                    shadowBlur: Number(newValue),
                  });
                }}
                min={0}
                max={50}
                step={1}
                sx={{ width: "50%" }}
              />
              <TextField
                value={shadowBlur}
                onChange={(e) => {
                  const newValue = Number(e.target.value);
                  setShadowBlur(newValue);
                  store.updateTextStyle(element.id, { shadowBlur: newValue });
                }}
                variant="outlined"
                size="small"
                sx={{ width: "50px" }}
              />
            </Stack>

            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2">Offset X</Typography>
              <Slider
                size="small"
                value={shadowOffsetX}
                onChange={(e, newValue) => {
                  setShadowOffsetX(Number(newValue));
                }}
                onChangeCommitted={(e, newValue) => {
                  setShadowOffsetX(Number(newValue));
                  store.updateTextStyle(element.id, {
                    shadowOffsetX: Number(newValue),
                  });
                }}
                min={-50}
                max={50}
                step={1}
                sx={{ width: "50%" }}
              />
              <TextField
                value={shadowOffsetX}
                onChange={(e) => {
                  const newValue = Number(e.target.value);
                  setShadowOffsetX(newValue);
                  store.updateTextStyle(element.id, {
                    shadowOffsetX: newValue,
                  });
                }}
                variant="outlined"
                size="small"
                sx={{ width: "50px" }}
              />
            </Stack>

            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2">Offset Y</Typography>
              <Slider
                size="small"
                value={shadowOffsetY}
                onChange={(e, newValue) => {
                  setShadowOffsetX(Number(newValue));
                }}
                onChangeCommitted={(e, newValue) => {
                  store.updateTextStyle(element.id, {
                    shadowOffsetY: Number(newValue),
                  });
                }}
                min={-50}
                max={50}
                step={1}
                sx={{ width: "50%" }}
              />
              <TextField
                value={shadowOffsetY}
                onChange={(e) => {
                  const newValue = Number(e.target.value);
                  setShadowOffsetY(newValue);
                  store.updateTextStyle(element.id, {
                    shadowOffsetY: newValue,
                  });
                }}
                variant="outlined"
                size="small"
                sx={{ width: "50px" }}
              />
            </Stack>
          </>
        )}
      </Stack>
    </Box>
  );
});

export default FontSetting;
