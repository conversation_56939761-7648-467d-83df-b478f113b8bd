import React, { useState } from "react";
import {
  <PERSON>,
  Typography,
  TextField,
  Grid,
  Button,
  IconButton,
  Slider,
  Paper,
  Divider,
  Stack,
  Checkbox,
  Tooltip,
} from "@mui/material";
import { styled } from "@mui/system";
import {
  ColorLens,
  Refresh,
  Check,
  AspectRatio,
  Gradient,
  FileDownload,
  FileUpload,
} from "@mui/icons-material";
import { StoreContext } from "../../store";
import ColorPicker from "../components/color/ColorPicker";

const DarkBox = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  height: "100vh",
  overflowY: "auto",
  backgroundColor: "background.paper",
}));

const StyledTextField = styled(TextField)({});

const PresetButton = styled(Button)(({ theme }) => ({
  height: "100%",
  display: "flex",
  flexDirection: "column",
  justifyContent: "center",
  alignItems: "center",
  borderColor: "background.paper",
  transition: "all 0.3s",
  "&:hover": {
    transform: "scale(1.05)",
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: "bold",
  marginBottom: theme.spacing(2),
}));

const CanvasSetting = () => {
  const store = React.useContext(StoreContext);
  const [backgroundColor, setBackgroundColor] = useState(store.backgroundColor);

  const [width, setWidth] = useState(store.canvasWidth.toString());
  const [height, setHeight] = useState(store.canvasHeight.toString());
  const [isGradient, setIsGradient] = useState(false);
  const [gradientColor1, setGradientColor1] = useState("#000000");
  const [gradientColor2, setGradientColor2] = useState("#ffffff");

  const [duration, setDuration] = useState(store.maxTime / 1000);
  const handleDurationChange = (event, newValue) => {
    setDuration(newValue);
    store.setMaxTime(newValue * 1000);
  };

  const handleWidthChange = (e) => {
    const newWidth = e.target.value;
    setWidth(newWidth);
    store.setCanvasSize(parseInt(newWidth), parseInt(height));
  };

  const handleHeightChange = (e) => {
    const newHeight = e.target.value;
    setHeight(newHeight);
    store.setCanvasSize(parseInt(width), parseInt(newHeight));
  };

  const handlePresetClick = (preset) => {
    const [presetWidth, presetHeight] = preset.size.split("x");
    setWidth(presetWidth);
    setHeight(presetHeight);
    store.setCanvasSize(parseInt(presetWidth), parseInt(presetHeight));
    store.saveChange();
  };

  const handleGradientToggle = () => {
    setIsGradient(!isGradient);
    updateBackgroundColor();
  };

  const updateBackgroundColor = () => {
    let newColor;
    if (isGradient) {
      newColor = `linear-gradient(to right, ${gradientColor1}, ${gradientColor2})`;
    } else {
      newColor = backgroundColor;
    }
    store.setBackgroundColor(newColor);
    store.canvas.requestRenderAll();
  };

  const handleGradientColorChange = (color: string, index: number) => {
    if (index === 1) {
      setGradientColor1(color);
    } else {
      setGradientColor2(color);
    }
    updateBackgroundColor();
    store.saveChange();
  };

  const handleColorChange = (color: string) => {
    setBackgroundColor(color);
    store.setBackgroundColor(color);
    store.saveChange();
    store.canvas.requestRenderAll();
  };

  const handleExport = () => {
    const state = store.exportCanvasState();
    if (state) {
      const blob = new Blob([state], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = "canvas-state.json";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const handleImport = () => {
    const input = document.createElement("input");
    input.type = "file";
    input.accept = ".json";
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          store.importCanvasState(content);
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  return (
    <DarkBox elevation={3}>
      <SectionTitle variant="subtitle1">Settings</SectionTitle>
      <Grid container spacing={2}>
        <Grid item xs={12} md={6}>
          <StyledTextField
            fullWidth
            label="Width"
            value={width}
            onChange={handleWidthChange}
            variant="outlined"
            size="small"
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <StyledTextField
            fullWidth
            label="Height"
            value={height}
            onChange={handleHeightChange}
            variant="outlined"
            size="small"
          />
        </Grid>
        <Grid item xs={12} sx={{ display: "flex", alignItems: "center" }}>
          <Stack direction="row" spacing={1} alignItems="center">
            <StyledTextField
              fullWidth
              label="Background"
              value={isGradient ? gradientColor1 : backgroundColor}
              variant="outlined"
              size="small"
              InputProps={{
                endAdornment: (
                  <>
                    <Tooltip title="Random Color">
                      <IconButton
                        onClick={() => {
                          if (isGradient) {
                            // Generate valid hex colors by padding with zeros if necessary
                            const randomColor1 =
                              "#" +
                              Math.floor(Math.random() * 16777215)
                                .toString(16)
                                .padStart(6, "0");
                            const randomColor2 =
                              "#" +
                              Math.floor(Math.random() * 16777215)
                                .toString(16)
                                .padStart(6, "0");
                            setGradientColor1(randomColor1);
                            setGradientColor2(randomColor2);
                            // Update background with new gradient colors
                            store.setBackgroundColor(
                              `linear-gradient(to right, ${randomColor1}, ${randomColor2})`
                            );
                            store.canvas.requestRenderAll(); // 确保画布更新
                          } else {
                            // Original behavior for solid color
                            const randomColor =
                              "#" +
                              Math.floor(Math.random() * 16777215)
                                .toString(16)
                                .padStart(6, "0");
                            handleColorChange(randomColor);
                          }
                        }}
                      >
                        <Refresh fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </>
                ),
              }}
            />
            {isGradient ? (
              <>
                <ColorPicker
                  color={gradientColor1}
                  onChange={(color) => handleGradientColorChange(color, 1)}
                />
                <ColorPicker
                  color={gradientColor2}
                  onChange={(color) => handleGradientColorChange(color, 2)}
                />
              </>
            ) : (
              <ColorPicker
                color={backgroundColor}
                onChange={handleColorChange}
              />
            )}
            <Tooltip title="Gradient">
              <Checkbox
                icon={<Gradient color="action" fontSize="small" />}
                checkedIcon={<Gradient color="primary" fontSize="small" />}
                checked={isGradient}
                onChange={handleGradientToggle}
                sx={{
                  m: 0.5,
                  p: 0.5,
                  borderRadius: 1,
                  backgroundColor: "rgba(0,0,0,0.03)",
                  border: "1px solid rgba(0,0,0,0.08)",
                  transition: "all 0.2s ease",
                  "&:hover": {
                    backgroundColor: "rgba(0,0,0,0.06)",
                    transform: "scale(1.03)",
                    boxShadow: "0 4px 8px rgba(0,0,0,0.1)",
                  },
                }}
              />
            </Tooltip>
          </Stack>
        </Grid>
        <Grid item xs={12}>
          <Divider sx={{ my: 1 }} />
        </Grid>
        {/* <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom>
            Duration: {duration} sec
          </Typography>
          <Slider
            value={duration}
            size="small"
            onChange={handleDurationChange}
            step={1}
            min={10}
            max={600}
            marks={[
              { value: 10, label: "10s" },
              { value: 300, label: "5m" },
              { value: 600, label: "10m" },
            ]}
            valueLabelDisplay="auto"
          />
        </Grid> */}
        <Grid item xs={6}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={<FileDownload />}
            onClick={handleExport}
          >
            Export
          </Button>
        </Grid>
        <Grid item xs={6}>
          <Button
            fullWidth
            variant="outlined"
            startIcon={<FileUpload />}
            onClick={handleImport}
          >
            Import
          </Button>
        </Grid>
      </Grid>
      <Grid item xs={12}>
        <Divider sx={{ my: 2 }} />
      </Grid>
      <Grid container spacing={2}>
        {[
          { ratio: "1:1", size: "1080x1080px" },
          { ratio: "1:1", size: "720x720px" },
          { ratio: "1:1", size: "600x600px" },
          { ratio: "4:5", size: "1080x1350px" },
          { ratio: "16:9", size: "1280x720px" },
          { ratio: "16:9", size: "640x360px" },
          { ratio: "9:16", size: "720x1280px" },
          { ratio: "9:16", size: "360x640px" },
          { ratio: "4:3", size: "640x480px" },
          { ratio: "4:3", size: "480x360px" },
        ].map((preset, index) => (
          <Grid item xs={12} sm={6} md={6} key={index}>
            <PresetButton
              fullWidth
              variant="outlined"
              onClick={() => handlePresetClick(preset)}
            >
              <Typography variant="body2">{preset.ratio}</Typography>
              <Typography variant="caption">{preset.size}</Typography>
            </PresetButton>
          </Grid>
        ))}
      </Grid>
    </DarkBox>
  );
};

export default CanvasSetting;
