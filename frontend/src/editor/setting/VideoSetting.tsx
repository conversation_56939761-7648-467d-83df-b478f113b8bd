import BorderAllIcon from "@mui/icons-material/BorderAll";
import CancelIcon from "@mui/icons-material/Cancel";
import CropIcon from "@mui/icons-material/Crop";
import RestartAltIcon from "@mui/icons-material/RestartAlt";
import {
  Box,
  IconButton,
  MenuItem,
  Select,
  SelectChangeEvent,
  Slider,
  Stack,
  Tab,
  Tabs,
  Tooltip,
  Typography,
} from "@mui/material";
import { styled } from "@mui/material/styles";
import { observer } from "mobx-react";
import React from "react";
import { StoreContext } from "../../store";
import { BorderStyle, EffecType } from "../../types";
import ColorPicker from "../components/color/ColorPicker";
import BaseSetting from "./BaseSetting";

const StyledBox = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  margin: theme.spacing(1),
  maxHeight: "100vh",
  overflowY: "auto",
  backgroundColor: theme.palette.background.paper,
  borderRadius: theme.shape.borderRadius,
}));

const StyledTypography = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  fontWeight: "bold",
  color: theme.palette.primary.main,
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  borderBottom: `1px solid ${theme.palette.divider}`,
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  minWidth: 65,
  fontWeight: theme.typography.fontWeightRegular,
  marginRight: theme.spacing(1),
  "&:hover": {
    color: theme.palette.primary.main,
    opacity: 1,
  },
  "&.Mui-selected": {
    color: theme.palette.primary.main,
    fontWeight: theme.typography.fontWeightMedium,
  },
}));

const EFFECT_TYPE_TO_LABEL: Record<string, string> = {
  none: "None",
  blackAndWhite: "Black and White",
  saturate: "Saturate",
  sepia: "Sepia",
  invert: "Invert",
};

const VideoSetting = observer(({ element }) => {
  const store = React.useContext(StoreContext);
  const [isCropping, setIsCropping] = React.useState(false);
  const [activeTab, setActiveTab] = React.useState(0);
  const [showBorderSettings, setShowBorderSettings] = React.useState(false);

  const handleCrop = () => {
    if (!isCropping) {
      store.startCropMode(element.id);
      setIsCropping(true);
    } else {
      store.applyCrop();
      setIsCropping(false);
    }
  };

  const handleCancelCrop = () => {
    store.cancelCrop();
    setIsCropping(false);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleBorderToggle = () => {
    setShowBorderSettings(!showBorderSettings);
  };

  const updateFilter = (filterType: string, value: number) => {
    store.setMediaFilter(
      element.id,
      filterType as "brightness" | "contrast" | "saturation" | "hue" | "blur",
      value
    );
  };

  const handleResetFilters = () => {
    const defaultFilters = {
      brightness: 0,
      contrast: 0,
      saturation: 0,
      hue: 0,
      blur: 0,
    };

    Object.entries(defaultFilters).forEach(([filterType, value]) => {
      updateFilter(filterType, value);
    });
  };

  const updateBorder = (
    property: keyof BorderStyle,
    value: string | number
  ) => {
    store.setMediaElementBorder(element.id, property, value);
  };

  return (
    <StyledBox>
      <StyledTypography variant="h6">Video Settings</StyledTypography>

      <StyledTabs value={activeTab} onChange={handleTabChange}>
        <StyledTab label="Basic" />
        <StyledTab label="Advanced" />
      </StyledTabs>

      {activeTab === 0 && (
        <Box>
          <Stack direction="row" spacing={2} sx={{ mb: 4 }}>
            <Tooltip title={isCropping ? "Apply Crop" : "Crop Video"}>
              <IconButton
                onClick={handleCrop}
                color={isCropping ? "secondary" : "primary"}
              >
                <CropIcon />
              </IconButton>
            </Tooltip>
            {isCropping && (
              <Tooltip title="Cancel Crop">
                <IconButton onClick={handleCancelCrop}>
                  <CancelIcon />
                </IconButton>
              </Tooltip>
            )}
            <Tooltip
              title={showBorderSettings ? "Hide Settings" : "Show Settings"}
            >
              <IconButton
                onClick={handleBorderToggle}
                color={showBorderSettings ? "secondary" : "primary"}
              >
                <BorderAllIcon />
              </IconButton>
            </Tooltip>
          </Stack>
          {showBorderSettings && (
            <Box sx={{ mb: 3 }}>
              <Stack spacing={2}>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography variant="body2" sx={{ mr: 2, minWidth: "40px" }}>
                    Width
                  </Typography>
                  <Slider
                    size="small"
                    value={element.properties.border?.width ?? 0}
                    min={0}
                    max={20}
                    onChange={(e, newValue) =>
                      updateBorder("width", Number(newValue))
                    }
                    sx={{ flex: 1 }}
                  />
                </Box>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography variant="body2" sx={{ mr: 2, minWidth: "40px" }}>
                    Color
                  </Typography>
                  <ColorPicker
                    color={element.properties.border?.color ?? "#000000"}
                    onChange={(color) => updateBorder("color", color)}
                  />
                </Box>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography variant="body2" sx={{ mr: 2, minWidth: "40px" }}>
                    Style
                  </Typography>
                  <Select
                    size="small"
                    value={element.properties.border?.style ?? "solid"}
                    onChange={(e) => updateBorder("style", e.target.value)}
                    fullWidth
                    renderValue={(selected) => (
                      <Box sx={{ display: "flex", alignItems: "center" }}>
                        <Box
                          sx={{
                            width: 12,
                            height: 12,
                            mr: 1,
                            border: `2px ${selected} black`,
                          }}
                        />
                        <Typography variant="body1" sx={{ fontSize: "1rem" }}>
                          {selected.charAt(0).toUpperCase() + selected.slice(1)}
                        </Typography>
                      </Box>
                    )}
                  >
                    {["solid", "dashed", "dotted"].map((style) => (
                      <MenuItem key={style} value={style}>
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                          <Box
                            sx={{
                              width: 12,
                              height: 12,
                              mr: 1,
                              border: `2px ${style} black`,
                            }}
                          />
                          <Typography variant="body1" sx={{ fontSize: "1rem" }}>
                            {style.charAt(0).toUpperCase() + style.slice(1)}
                          </Typography>
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <Typography variant="body2" sx={{ mr: 2, minWidth: "40px" }}>
                    Radius
                  </Typography>
                  <Slider
                    size="small"
                    value={element.properties.border?.borderRadius ?? 0}
                    min={0}
                    max={100}
                    onChange={(e, newValue) =>
                      updateBorder("borderRadius", Number(newValue))
                    }
                    sx={{ flex: 1 }}
                  />
                </Box>
              </Stack>
            </Box>
          )}
          <BaseSetting element={element} />
        </Box>
      )}

      {activeTab === 1 && (
        <Box>
          <Stack
            direction="row"
            spacing={1}
            sx={{
              justifyContent: "flex-start",
              alignItems: "center",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
              Filters
            </Typography>
            <IconButton onClick={handleResetFilters} size="small">
              <RestartAltIcon />
            </IconButton>
          </Stack>
          <Box sx={{ mb: 2 }}>
            <Select
              size="small"
              value={element.properties.effect.type}
              onChange={(e: SelectChangeEvent<string>) => {
                const type = e.target.value;
                store.updateEffect(element.id, {
                  type: type as EffecType,
                });
              }}
              sx={{
                width: "100%",
              }}
            >
              {Object.entries(EFFECT_TYPE_TO_LABEL).map(([type, label]) => (
                <MenuItem key={type} value={type}>
                  {label}
                </MenuItem>
              ))}
            </Select>
          </Box>
          {["Brightness", "Contrast", "Saturation", "Hue", "Blur"].map(
            (filter) => (
              <Box key={filter} sx={{ p: 0, m: 0 }}>
                <Typography gutterBottom variant="body2">
                  {filter}
                </Typography>
                <Slider
                  size="small"
                  value={
                    element.properties.filters?.[filter.toLowerCase()] ?? 0
                  }
                  min={filter === "Blur" ? 0 : -100}
                  max={100}
                  onChangeCommitted={(e, newValue) =>
                    updateFilter(filter.toLowerCase(), Number(newValue))
                  }
                />
              </Box>
            )
          )}
        </Box>
      )}
    </StyledBox>
  );
});

export default VideoSetting;
