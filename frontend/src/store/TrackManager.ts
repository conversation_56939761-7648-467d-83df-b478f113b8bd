import { makeAutoObservable } from "mobx";
import { Track, EditorElement, TrackType } from "../types";
import { getUid } from "../utils";
import { timeStringToMs } from "../utils/timeUtils";

export class TrackManager {
  private store: any;
  tracks: Track[] = [];
  defaultTracks: Record<TrackType, string> = {
    media: "",
    audio: "",
    text: "",
    caption: "",
  };

  constructor(store: any) {
    this.store = store;
    this.tracks = [];
    makeAutoObservable(this);
  }

  /**
   * 初始化轨道，创建默认轨道并为现有元素分配轨道
   */
  initializeTracks() {
    // 清空现有轨道
    this.tracks = [];

    // 创建默认轨道
    this.createDefaultTracks();

    // 为现有元素分配轨道
    this.assignElementsToTracks();
  }

  /**
   * 创建默认轨道（媒体、音频、文本、字幕）
   */
  createDefaultTracks() {
    // const trackTypes: TrackType[] = ["media"];
    // trackTypes.forEach((type) => {
    //   const track = this.createTrack(
    //     type,
    //     `${type.charAt(0).toUpperCase() + type.slice(1)} Track`
    //   );
    //   this.defaultTracks[type] = track.id;
    // });
  }

  /**
   * 将现有元素分配到相应类型的默认轨道
   */
  assignElementsToTracks() {
    // 先处理字幕
    if (this.store.captions && this.store.captions.length > 0) {
      const captionTrackId = this.defaultTracks.caption;
      if (captionTrackId) {
        const captionTrack = this.tracks.find((t) => t.id === captionTrackId);
        if (captionTrack) {
          // 字幕不需要添加到轨道的elementIds中，因为它们由CaptionsTrackView单独处理
        }
      }
    }

    // 处理其他元素
    this.store.editorElements.forEach((element: EditorElement) => {
      // 将image和video类型的元素分配到media轨道
      let trackType: TrackType = element.type as TrackType;
      if (element.type === "image" || element.type === "video") {
        trackType = "media";
      }

      const defaultTrackId = this.defaultTracks[trackType];
      if (defaultTrackId) {
        this.addElementToTrack(defaultTrackId, element.id);
      } else {
        // 如果没有对应类型的默认轨道，创建一个新轨道
        const track = this.createTrack(trackType);
        this.addElementToTrack(track.id, element.id);
      }
    });
  }

  /**
   * 创建一个新的轨道
   * @param type 轨道类型
   * @param name 轨道名称（可选）
   * @returns 新创建的轨道
   */
  createTrack(type: TrackType, name?: string): Track {
    const trackId = getUid();
    const track: Track = {
      id: trackId,
      name:
        name ||
        `${type.charAt(0).toUpperCase() + type.slice(1)} Track ${
          this.getTrackCountByType(type) + 1
        }`,
      type,
      elementIds: [],
      isVisible: true,
      isLocked: false,
    };
    this.tracks.push(track);
    return track;
  }

  /**
   * 获取指定类型的轨道数量
   * @param type 轨道类型
   * @returns 轨道数量
   */
  getTrackCountByType(type: TrackType): number {
    return this.tracks.filter((track) => track.type === type).length;
  }

  /**
   * 向轨道添加元素
   * @param trackId 轨道ID
   * @param elementId 元素ID
   */
  addElementToTrack(trackId: string, elementId: string) {
    const track = this.tracks.find((t) => t.id === trackId);
    if (!track) return;

    // 确保元素不在其他轨道中
    this.removeElementFromAllTracks(elementId);

    // 添加到指定轨道
    track.elementIds.push(elementId);

    // 更新元素的trackId属性
    const element = this.store.editorElements.find(
      (el: EditorElement) => el.id === elementId
    );
    if (element) {
      element.trackId = trackId;
    }
  }

  /**
   * 从所有轨道中移除元素
   * @param elementId 元素ID
   */
  removeElementFromAllTracks(elementId: string) {
    this.tracks.forEach((track) => {
      const index = track.elementIds.indexOf(elementId);
      if (index !== -1) {
        track.elementIds.splice(index, 1);

        // 清除元素的trackId属性
        const element = this.store.editorElements.find(
          (el: EditorElement) => el.id === elementId
        );
        if (element) {
          element.trackId = undefined;
        }
      }
    });
  }

  /**
   * 删除轨道
   * @param trackId 轨道ID
   */
  deleteTrack(trackId: string) {
    // 不允许删除默认轨道
    if (Object.values(this.defaultTracks).includes(trackId)) {
      return;
    }

    const index = this.tracks.findIndex((t) => t.id === trackId);
    if (index === -1) return;

    // 将轨道中的元素移动到默认轨道
    const track = this.tracks[index];
    track.elementIds.forEach((elementId) => {
      const element = this.store.editorElements.find(
        (el: EditorElement) => el.id === elementId
      );
      if (element) {
        // 确定轨道类型
        let trackType: TrackType;
        if (element.type === "image" || element.type === "video") {
          trackType = "media";
        } else {
          trackType = element.type as TrackType;
        }

        const defaultTrackId = this.defaultTracks[trackType];
        if (defaultTrackId) {
          this.addElementToTrack(defaultTrackId, elementId);
        }
      }
    });

    this.tracks.splice(index, 1);

    // 由于轨道结构发生了变化，需要更新Canvas上的元素显示顺序
    this.store.updateCanvasOrderByTrackOrder();
  }

  /**
   * 获取元素所在的轨道
   * @param elementId 元素ID
   * @returns 元素所在的轨道，如果不在任何轨道中则返回undefined
   */
  getTrackByElementId(elementId: string): Track | undefined {
    return this.tracks.find((track) => track.elementIds.includes(elementId));
  }

  /**
   * 获取轨道中的所有元素
   * @param trackId 轨道ID
   * @returns 轨道中的所有元素
   */
  getElementsByTrackId(trackId: string): EditorElement[] {
    const track = this.tracks.find((t) => t.id === trackId);
    if (!track) return [];

    return track.elementIds
      .map((id) =>
        this.store.editorElements.find((el: EditorElement) => el.id === id)
      )
      .filter(Boolean);
  }

  /**
   * 获取所有轨道中的元素，按轨道分组
   * @returns 按轨道分组的元素
   */
  getAllTrackElements(): { track: Track; elements: EditorElement[] }[] {
    return this.tracks.map((track) => ({
      track,
      elements: this.getElementsByTrackId(track.id),
    }));
  }

  /**
   * 获取所有元素，按轨道从上到下，同一轨道从左到右排序
   * @returns 排序后的元素数组
   */
  getAllElementsInDisplayOrder(): EditorElement[] {
    // 获取所有轨道及其元素
    const trackElements = this.getAllTrackElements();

    // 创建结果数组
    const result: EditorElement[] = [];

    // 遍历每个轨道（从下到上）
    for (let i = trackElements.length - 1; i >= 0; i--) {
      const { track, elements } = trackElements[i];

      // 对轨道内的元素按时间排序（从右到左）
      const sortedElements = [...elements].sort(
        (a, b) => b.timeFrame.start - a.timeFrame.start
      );

      // 将排序后的元素添加到结果数组
      result.push(...sortedElements);
    }

    return result;
  }

  /**
   * 当添加新元素时，将其添加到适当的轨道，并进行智能碰撞检测
   * @param element 新添加的元素
   */
  handleNewElement(element: EditorElement) {
    // 将image和video类型的元素分配到media轨道
    let trackType: TrackType = element.type as TrackType;
    if (element.type === "image" || element.type === "video") {
      trackType = "media";
    }

    let targetTrackId = "";

    // 如果有选中的元素
    if (this.store.selectedElement) {
      const selectedElementTrack = this.getTrackByElementId(
        this.store.selectedElement.id
      );

      // 获取选中元素的类型
      let selectedElementType: TrackType = this.store.selectedElement
        .type as TrackType;
      if (
        this.store.selectedElement.type === "image" ||
        this.store.selectedElement.type === "video"
      ) {
        selectedElementType = "media";
      }

      // 如果新元素类型和选中元素类型一样，添加到相同轨道
      if (trackType === selectedElementType && selectedElementTrack) {
        targetTrackId = selectedElementTrack.id;
      } else {
        // 类型不同，创建新轨道
        const track = this.createTrack(trackType);
        targetTrackId = track.id;
      }
    } else {
      // 没有选中元素，添加到对应类型的默认轨道
      const defaultTrackId = this.defaultTracks[trackType];
      if (defaultTrackId) {
        targetTrackId = defaultTrackId;
      } else {
        // 如果没有对应类型的默认轨道，创建一个新轨道
        const track = this.createTrack(trackType);
        targetTrackId = track.id;
      }
    }

    // 记录原始时间范围
    const originalTimeFrame = {
      start: element.timeFrame.start,
      end: element.timeFrame.end,
    };

    // 智能碰撞检测：检查目标轨道中是否有时间重叠的元素
    // 注意：如果元素从0开始，且与第一个元素重叠，checkElementOverlap会自动平推右侧元素
    const { startTime, endTime, hasOverlap } = this.checkElementOverlap(
      element,
      targetTrackId
    );

    // 如果有碰撞，调整元素的时间范围
    if (hasOverlap) {
      console.log(
        `检测到时间碰撞，调整元素时间从 ${element.timeFrame.start}-${element.timeFrame.end} 到 ${startTime}-${endTime}`
      );

      // 更新元素的时间范围以避免碰撞
      element.timeFrame = {
        start: startTime,
        end: endTime,
      };

      // 更新最大时间
      this.store.updateMaxTime();
    }

    // 添加元素到目标轨道
    this.addElementToTrack(targetTrackId, element.id);

    // 记录碰撞检测结果供调试
    return {
      targetTrackId,
      hasOverlap,
      originalTime: {
        start: originalTimeFrame.start,
        end: originalTimeFrame.end,
      },
      adjustedTime: {
        start: startTime,
        end: endTime,
      },
    };
  }

  /**
   * 当删除元素时，从轨道中移除
   * @param elementId 被删除的元素ID
   */
  handleElementDeleted(elementId: string) {
    this.removeElementFromAllTracks(elementId);

    // 删除非默认的空轨道
    this.removeEmptyTracks();
  }

  /**
   * 检查并删除所有空轨道（不包括默认轨道）
   */
  removeEmptyTracks() {
    const emptyTracks = this.tracks.filter(
      (track) =>
        track.elementIds.length === 0 &&
        !Object.values(this.defaultTracks).includes(track.id)
    );

    if (emptyTracks.length > 0) {
      emptyTracks.forEach((track) => this.deleteTrack(track.id));

      // 由于轨道结构发生了变化，需要更新Canvas上的元素显示顺序
      this.store.updateCanvasOrderByTrackOrder();
    }

    return emptyTracks.length > 0; // 返回是否删除了轨道
  }

  /**
   * 检查两个元素是否在同一轨道
   * @param elementId1 元素1的ID
   * @param elementId2 元素2的ID
   * @returns 是否在同一轨道
   */
  areElementsInSameTrack(elementId1: string, elementId2: string): boolean {
    const track = this.getTrackByElementId(elementId1);
    return track ? track.elementIds.includes(elementId2) : false;
  }

  /**
   * 检查元素在时间线上是否重叠
   * @param element1 元素1
   * @param element2 元素2
   * @returns 是否重叠
   */
  areElementsOverlapping(
    element1: EditorElement,
    element2: EditorElement
  ): boolean {
    return (
      (element1.timeFrame.start <= element2.timeFrame.start &&
        element2.timeFrame.start < element1.timeFrame.end) ||
      (element2.timeFrame.start <= element1.timeFrame.start &&
        element1.timeFrame.start < element2.timeFrame.end)
    );
  }

  /**
   * 获取轨道中的元素，按时间排序
   * @param trackId 轨道ID
   * @returns 按时间排序的元素
   */
  getSortedElementsByTrackId(trackId: string): EditorElement[] {
    const elements = this.getElementsByTrackId(trackId);
    return elements.sort((a, b) => a.timeFrame.start - b.timeFrame.start);
  }

  /**
   * 检查元素在轨道中是否与其他元素重叠，并提供调整建议
   * @param element 要检查的元素
   * @param trackId 轨道ID
   * @returns 调整后的时间范围和是否有重叠
   */
  checkElementOverlap(
    element: EditorElement,
    trackId: string
  ): { startTime: number; endTime: number; hasOverlap: boolean } {
    const track = this.tracks.find((t) => t.id === trackId);
    if (!track) {
      return {
        startTime: element.timeFrame.start,
        endTime: element.timeFrame.end,
        hasOverlap: false,
      };
    }

    const trackElements = this.getSortedElementsByTrackId(trackId).filter(
      (el) => el.id !== element.id
    );

    if (trackElements.length === 0) {
      return {
        startTime: element.timeFrame.start,
        endTime: element.timeFrame.end,
        hasOverlap: false,
      };
    }

    let adjustedStartTime = element.timeFrame.start;
    let adjustedEndTime = element.timeFrame.end;
    let hasOverlap = false;
    const duration = element.timeFrame.end - element.timeFrame.start;

    // 找出所有可能重叠的元素
    const overlappingElements = trackElements.filter((otherElement) => {
      return this.areElementsOverlapping(element, otherElement);
    });

    // 特殊情况处理：如果元素从0开始
    if (element.timeFrame.start === 0) {
      // 获取轨道中的第一个元素（按开始时间排序）
      const firstElement = trackElements.length > 0 ? trackElements[0] : null;

      if (firstElement) {
        // 检查从0开始到第一个元素的间隔是否小于新元素的duration
        const availableSpace = firstElement.timeFrame.start;

        if (availableSpace < duration) {
          console.log(
            `检测到从0开始的元素需要 ${duration}ms 空间，但只有 ${availableSpace}ms 可用，将向右平推所有元素`
          );

          // 计算需要平移的距离
          const pushDistance = duration - availableSpace;

          // 向右平推所有元素
          this.pushElementsRight(trackId, pushDistance);

          // 返回原始时间范围，因为我们已经平推了其他元素
          return {
            startTime: element.timeFrame.start,
            endTime: element.timeFrame.end,
            hasOverlap: false, // 设置为false，因为我们已经解决了重叠问题
          };
        }
      }
    }

    if (overlappingElements.length > 0) {
      hasOverlap = true;

      // 按照与当前元素的距离排序
      const sortedOverlappingElements = overlappingElements.sort((a, b) => {
        const distanceA = Math.min(
          Math.abs(element.timeFrame.start - a.timeFrame.end),
          Math.abs(element.timeFrame.end - a.timeFrame.start)
        );
        const distanceB = Math.min(
          Math.abs(element.timeFrame.start - b.timeFrame.end),
          Math.abs(element.timeFrame.end - b.timeFrame.start)
        );
        return distanceA - distanceB;
      });

      // 获取最近的重叠元素
      const nearest = sortedOverlappingElements[0];

      // 计算调整方向
      const distanceToEnd = Math.abs(
        element.timeFrame.start - nearest.timeFrame.end
      );
      const distanceToStart = Math.abs(
        element.timeFrame.end - nearest.timeFrame.start
      );

      if (distanceToEnd <= distanceToStart) {
        // 将元素移动到重叠元素的后面
        adjustedStartTime = nearest.timeFrame.end;
        adjustedEndTime = adjustedStartTime + duration;
      } else {
        // 将元素移动到重叠元素的前面
        adjustedEndTime = nearest.timeFrame.start;
        adjustedStartTime = adjustedEndTime - duration;
      }

      // 确保调整后的时间不为负
      if (adjustedStartTime < 0) {
        adjustedStartTime = 0;
        adjustedEndTime = duration;
      }
    }

    return {
      startTime: adjustedStartTime,
      endTime: adjustedEndTime,
      hasOverlap,
    };
  }

  /**
   * 向右平推轨道中的所有元素
   * @param trackId 轨道ID
   * @param distance 平推距离（毫秒）
   */
  pushElementsRight(trackId: string, distance: number) {
    // 获取轨道中的所有元素，按开始时间排序
    const elements = this.getSortedElementsByTrackId(trackId);

    if (elements.length === 0) return;

    console.log(
      `向右平推轨道 ${trackId} 中的 ${elements.length} 个元素，距离 ${distance}ms`
    );

    // 从左到右依次平推每个元素
    for (const element of elements) {
      const newStartTime = element.timeFrame.start + distance;
      const newEndTime = element.timeFrame.end + distance;

      console.log(
        `平推元素 ${element.id}，从 ${element.timeFrame.start}-${element.timeFrame.end} 到 ${newStartTime}-${newEndTime}`
      );

      // 更新元素的时间范围，并标记为拖拽结束以触发maxDuration更新
      this.store.updateEditorElementTimeFrame(
        element,
        {
          start: newStartTime,
          end: newEndTime,
        },
        true
      );
    }
  }

  /**
   * 修复轨道中所有元素的重叠问题
   * @param trackId 轨道ID
   */
  fixTrackElementsOverlap(trackId: string) {
    const elements = this.getSortedElementsByTrackId(trackId);

    if (elements.length <= 1) return;

    // 逐个检查并修复重叠
    for (let i = 1; i < elements.length; i++) {
      const currentElement = elements[i];
      const previousElement = elements[i - 1];

      // 检查是否有重叠
      if (this.areElementsOverlapping(currentElement, previousElement)) {
        // 计算当前元素的持续时间
        const currentDuration =
          currentElement.timeFrame.end - currentElement.timeFrame.start;

        // 调整当前元素的开始时间
        const newStartTime = previousElement.timeFrame.end + 10; // 添加10ms的间隔
        const newEndTime = newStartTime + currentDuration;

        // 更新元素的时间范围，并标记为拖拽结束以触发maxDuration更新
        this.store.updateEditorElementTimeFrame(
          currentElement,
          {
            start: newStartTime,
            end: newEndTime,
          },
          true
        );

        // 递归检查后续元素，确保所有重叠都被修复
        this.fixTrackElementsOverlap(trackId);
        return; // 提前返回，避免重复处理
      }
    }
  }

  /**
   * 在指定位置创建新轨道
   * @param type 轨道类型
   * @param position 位置索引，如果为-1则添加到末尾
   * @param name 轨道名称（可选）
   * @returns 新创建的轨道
   */
  createTrackAtPosition(
    type: TrackType,
    position: number = -1,
    name?: string
  ): Track {
    const track = this.createTrack(type, name);

    // 如果指定了位置，则将轨道移动到该位置
    if (position >= 0 && position < this.tracks.length) {
      // 由于createTrack已经将轨道添加到末尾，我们需要先移除它
      const tracks = [...this.tracks];
      const newTrack = tracks.pop()!; // 移除最后添加的轨道

      // 在指定位置插入轨道
      tracks.splice(position, 0, newTrack);
      this.tracks = tracks;

      // 由于轨道顺序发生了变化，需要更新Canvas上的元素显示顺序
      this.store.updateCanvasOrderByTrackOrder();
    }

    return track;
  }

  /**
   * 将元素从一个轨道移动到另一个轨道
   * @param elementId 要移动的元素ID
   * @param targetTrackId 目标轨道ID
   * @returns 是否成功移动
   */
  moveElementToTrack(elementId: string, targetTrackId: string): boolean {
    // 获取元素
    const element = this.store.editorElements.find(
      (el: EditorElement) => el.id === elementId
    );
    if (!element) return false;

    // 获取目标轨道
    const targetTrack = this.tracks.find((t) => t.id === targetTrackId);
    if (!targetTrack) return false;

    // 检查元素类型是否与轨道类型匹配
    // 注意：我们允许不同类型的元素放在同一轨道，但会在UI上给出提示
    let isTypeMatch = false;

    // 对于image和video类型的元素，它们可以放在media类型的轨道中
    if (
      (element.type === "image" || element.type === "video") &&
      targetTrack.type === "media"
    ) {
      isTypeMatch = true;
    } else {
      isTypeMatch = element.type === targetTrack.type;
    }

    // 将元素添加到目标轨道
    this.addElementToTrack(targetTrackId, elementId);

    // 检查并修复目标轨道中的元素重叠
    this.fixTrackElementsOverlap(targetTrackId);

    // 根据轨道顺序更新Canvas上的元素显示顺序
    this.store.updateCanvasOrderByTrackOrder();

    // 选中移动后的元素
    this.store.setSelectedElement(element);

    return true;
  }

  /**
   * 清理轨道中无效的元素ID（不存在于editorElements数组中的ID）
   * @returns 被清理的元素ID数量
   */
  cleanupInvalidElementIds(): number {
    let removedCount = 0;

    // 遍历所有轨道
    this.tracks.forEach((track) => {
      // 找出轨道中存在但在editorElements中不存在的元素ID
      const invalidElementIds = track.elementIds.filter(
        (elementId) =>
          !this.store.editorElements.some((element) => element.id === elementId)
      );

      // 如果有无效的元素ID
      if (invalidElementIds.length > 0) {
        // 记录日志
        console.log(
          `从轨道 "${track.name}" 中移除 ${invalidElementIds.length} 个无效的元素ID`
        );

        // 从轨道中移除这些ID
        track.elementIds = track.elementIds.filter(
          (elementId) => !invalidElementIds.includes(elementId)
        );

        // 更新计数
        removedCount += invalidElementIds.length;
      }
    });

    // 如果有元素被移除，可能需要删除空轨道
    if (removedCount > 0) {
      this.removeEmptyTracks();
    }

    return removedCount;
  }
}
