import { fabric } from "fabric";
import {
  EditorElement,
  Placement,
  TextEditorElement,
  BorderStyle,
  ShapeEditorElement,
} from "../types";
import { Store } from "./Store";
import { getUid, isHtmlImageElement, isHtmlVideoElement } from "../utils";
import {
  CoverImage,
  CoverVideo,
  TextboxWithPadding,
} from "../utils/fabric-utils";
import { isEditorImageElement, isEditorMediaElement } from "../utils";
import { HistoryActionType } from "./HistoryManager";

export class ElementManager {
  private store: Store;
  private canvas: fabric.Canvas;

  constructor(store: Store) {
    this.store = store;
  }
  setCanvas(canvas: fabric.Canvas) {
    this.canvas = canvas;
  }

  /**
   * 计算正多边形的顶点坐标
   * @param sides 边数
   * @param radius 半径
   * @returns 顶点坐标数组
   */
  calculatePolygonPoints(sides: number, radius: number) {
    const points = [];
    const angleStep = (2 * Math.PI) / sides;

    for (let i = 0; i < sides; i++) {
      const angle = i * angleStep - Math.PI / 2; // 从顶部开始
      points.push({
        x: radius + radius * Math.cos(angle),
        y: radius + radius * Math.sin(angle),
      });
    }

    return points;
  }

  alignElement(id: string, alignType: string) {
    const element = this.store.editorElements.find((el) => el.id === id);
    if (!element || !element.fabricObject || !this.canvas) return;

    const object = element.fabricObject;
    const canvas = this.canvas;

    switch (alignType) {
      case "left":
        object.set({ left: 0 });
        break;
      case "center":
        object.centerH();
        break;
      case "right":
        object.set({ left: canvas.width - object.width * object.scaleX });
        break;
      case "top":
        object.set({ top: 0 });
        break;
      case "middle":
        object.centerV();
        break;
      case "bottom":
        object.set({ top: canvas.height - object.height * object.scaleY });
        break;
      case "justify":
        object.center();
        break;
    }
    element.placement = {
      x: object.left,
      y: object.top,
      width: object.width,
      height: object.height,
      rotation: object.angle,
      scaleX: object.scaleX,
      scaleY: object.scaleY,
      flipX: object.flipX,
      flipY: object.flipY,
    };
    // this.store.updateEditorElement({
    //   ...element,
    //   placement: {
    //     x: object.left,
    //     y: object.top,
    //     width: object.width,
    //     height: object.height,
    //     rotation: object.angle,
    //     scaleX: object.scaleX,
    //     scaleY: object.scaleY,
    //     flipX: object.flipX,
    //     flipY: object.flipY,
    //   },
    // });
    this.canvas.renderAll();
  }

  toggleLockElement(id: string) {
    const element = this.store.editorElements.find((el) => el.id === id);
    if (!element || !element.fabricObject) return;

    element.locked = !element.locked;
    element.fabricObject.set({
      selectable: !element.locked,
      lockMovementX: element.locked,
      lockMovementY: element.locked,
      lockRotation: element.locked,
      lockScalingX: element.locked,
      lockScalingY: element.locked,
    });

    this.store.setSelectedElement(null);
    this.canvas.renderAll();
  }

  cloneElement(id: string) {
    const store = this;
    const element = this.store.editorElements.find((el) => el.id === id);
    if (!element || !element.fabricObject) return;

    element.fabricObject.clone((clonedObj: fabric.Object) => {
      clonedObj.set({
        left: clonedObj.left! + 20,
        top: clonedObj.top! + 20,
      });
      // this.canvas.add(clonedObj);

      const newElement = {
        ...element,
        id: getUid(),
        fabricObject: clonedObj,
        placement: {
          ...element.placement,
          x: clonedObj.left!,
          y: clonedObj.top!,
        },
      };
      this.store.editorElements.push(newElement);

      store.addElement(newElement);
      this.store.setSelectedElement(null);
    });
  }

  deleteElement(id: string) {
    const elementIndex = this.store.editorElements.findIndex(
      (el) => el.id === id
    );
    if (elementIndex === -1) return;
    const element = this.store.editorElements[elementIndex];

    // 保存当前时间，用于后续检查
    const currentTime = this.store.currentTimeInMs;

    if (element.fabricObject) {
      this.canvas.remove(element.fabricObject);
    }

    if (
      element.type === "image" ||
      element.type === "video" ||
      element.type === "audio"
    ) {
      const htmlElement = document.getElementById(
        element.properties?.elementId
      );
      if (htmlElement) {
        htmlElement.remove();
      }
    }

    // 使用setEditorElements方法而不是直接修改数组
    this.store.setEditorElements(
      this.store.editorElements.filter((el) => el.id !== id)
    );

    this.store.setSelectedElement(null);
    this.canvas.renderAll();

    // 更新最大时间
    this.store.updateMaxTime();

    // 如果当前时间超过了新的最大endtime，则调整indicator位置到最大endtime
    if (this.store.maxDuration > 0 && currentTime > this.store.maxDuration) {
      this.store.handleSeek(this.store.maxDuration);
    }

    return element;
  }

  setElementFullscreen(id: string) {
    const element = this.store.editorElements.find((el) => el.id === id);
    if (!element || !element.fabricObject) return;

    const canvasWidth = this.canvas.getWidth();
    const canvasHeight = this.canvas.getHeight();

    element.fabricObject.set({
      width: canvasWidth,
      height: canvasHeight,
      scaleX: 1,
      scaleY: 1,
      left: 0,
      top: 0,
      angle: 0,
      flipX: element.fabricObject.flipX,
      flipY: element.fabricObject.flipY,
    });

    element.placement = {
      x: 0,
      y: 0,
      width: canvasWidth,
      height: canvasHeight,
      rotation: 0,
      scaleX: 1,
      scaleY: 1,

      flipX: element.fabricObject.flipX,
      flipY: element.fabricObject.flipY,
    };

    this.store.setSelectedElement(element);
    this.canvas.renderAll();
  }

  updateElementOpacity(id: string, opacity: number) {
    const element = this.store.editorElements.find((el) => el.id === id);
    if (element && element.fabricObject) {
      element.opacity = opacity;
      element.fabricObject.set("opacity", opacity);
      this.canvas.renderAll();
    }
  }

  moveElement(
    element: EditorElement,
    direction: "up" | "down" | "top" | "bottom"
  ) {
    const index = this.store.editorElements.indexOf(element);
    const elements = this.store.editorElements;

    switch (direction) {
      case "down":
        if (index > 0) {
          [elements[index - 1], elements[index]] = [
            elements[index],
            elements[index - 1],
          ];
        }
        break;
      case "up":
        if (index < elements.length - 1) {
          [elements[index], elements[index + 1]] = [
            elements[index + 1],
            elements[index],
          ];
        }
        break;
      case "top":
        if (index > 0) {
          const [movedElement] = elements.splice(index, 1);
          elements.unshift(movedElement);
        }
        break;
      case "bottom":
        if (index < elements.length - 1) {
          const [movedElement] = elements.splice(index, 1);
          elements.push(movedElement);
        }
        break;
    }

    this.store.setEditorElements([...elements]);
    this.updateCanvasOrder();
  }

  updateCanvasOrder() {
    this.store.editorElements.forEach((element, index) => {
      if (element.fabricObject) {
        this.canvas.moveTo(element.fabricObject, index);
      }
    });
    this.canvas.renderAll();
  }

  updateElementPlacement(element: EditorElement, object: fabric.Object) {
    const newPlacement: Placement = {
      x: object.left!,
      y: object.top!,
      width: object.width! * object.scaleX!,
      height: object.height! * object.scaleY!,
      rotation: object.angle!,
      scaleX: object.scaleX!,
      scaleY: object.scaleY!,
      flipX: object.flipX,
      flipY: object.flipY,
    };
    this.store.updateEditorElement({ ...element, placement: newPlacement });
  }

  updateTextStyle(
    elementId: string,
    style: Partial<TextEditorElement["properties"]>
  ) {
    const element = this.store.editorElements.find((el) => el.id === elementId);
    if (element && element.type === "text") {
      if (element.fabricObject instanceof fabric.TextboxWithPadding) {
        const textObject = element.fabricObject;

        if (style.fontSize) textObject.set("fontSize", style.fontSize);
        if (style.textAlign) textObject.set("textAlign", style.textAlign);
        if (style.charSpacing) textObject.set("charSpacing", style.charSpacing);
        if (style.lineHeight) textObject.set("lineHeight", style.lineHeight);

        if (style.styles) {
          const newStyles = {
            fontWeight: style.styles.includes("bold") ? "bold" : "normal",
            fontStyle: style.styles.includes("italic") ? "italic" : "normal",
            underline: style.styles.includes("underlined"),
            linethrough: style.styles.includes("strikethrough"),
          };
          textObject.set({
            ...newStyles,
            fontStyle: newStyles.fontStyle as
              | ""
              | "normal"
              | "italic"
              | "oblique",
          });
        }
        if (style.fontColor) textObject.set("fill", style.fontColor);
        if (style.fontFamily) textObject.set("fontFamily", style.fontFamily);
        if (style.strokeWidth !== undefined)
          textObject.set("strokeWidth", style.strokeWidth);
        if (style.strokeColor) textObject.set("stroke", style.strokeColor);

        if (
          style.shadowColor !== undefined ||
          style.shadowBlur !== undefined ||
          style.shadowOffsetX !== undefined ||
          style.shadowOffsetY !== undefined
        ) {
          textObject.set(
            "shadow",
            new fabric.Shadow({
              color:
                style.shadowColor ||
                element.properties.shadowColor ||
                "#000000",
              blur: style.shadowBlur || element.properties.shadowBlur || 0,
              offsetX:
                style.shadowOffsetX || element.properties.shadowOffsetX || 0,
              offsetY:
                style.shadowOffsetY || element.properties.shadowOffsetY || 0,
            })
          );
        }

        if (style.useGradient !== undefined) {
          console.log(style.useGradient);
          if (style.useGradient && style.gradientColors) {
            const gradient = new fabric.Gradient({
              type: "linear",
              coords: { x1: 0, y1: 0, x2: textObject.width, y2: 0 },
              colorStops: [
                { offset: 0, color: style.gradientColors[0] },
                { offset: 1, color: style.gradientColors[1] },
              ],
            });
            textObject.set("fill", gradient);
          } else if (style.fontColor) {
            textObject.set("fill", style.fontColor);
          }
        }
        if (style.backgroundColor) {
          textObject.set("backgroundColor", style.backgroundColor);
          //@ts-ignore
          textObject.set("rx", 10);
          //@ts-ignore
          textObject.set("ry", 10);
        }
        textObject.setCoords();
        this.canvas.requestRenderAll();
        //this.store.updateEditorElement(element);
      }
      element.properties = { ...element.properties, ...style };
    }
  }

  reorderElements(startIndex: number, endIndex: number) {
    const result = Array.from(this.store.editorElements);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    this.store.setEditorElements(result);
    this.updateCanvasOrder();
  }

  startCropMode(id: string) {
    const element = this.store.editorElements.find((el) => el.id === id);
    if (!element || !element.fabricObject || !this.canvas) return;

    this.store.cropObject = element.fabricObject;
    const obj = this.store.cropObject;

    // Create crop rectangle
    this.store.cropRect = new fabric.Rect({
      left: obj.left,
      top: obj.top,
      width: obj.width! * obj.scaleX!,
      height: obj.height! * obj.scaleY!,
      fill: "rgba(0,0,0,0.3)",
      stroke: "#fff",
      strokeWidth: 2,
      strokeDashArray: [5, 5],
      selectable: true,
      hasControls: true,
    });

    this.canvas.add(this.store.cropRect);
    this.canvas.setActiveObject(this.store.cropRect);

    // Add event listener for object scaling
    this.canvas.on("object:scaling", (e) => {
      if (e.target === this.store.cropRect) {
        const rect = e.target as fabric.Rect;
        rect.set({
          width: rect.width! * rect.scaleX!,
          height: rect.height! * rect.scaleY!,
          scaleX: 1,
          scaleY: 1,
        });
      }
    });

    this.canvas.renderAll();
  }

  applyCrop() {
    if (!this.store.cropObject || !this.store.cropRect || !this.canvas) return;

    const obj = this.store.cropObject as fabric.CoverImage;
    const rect = this.store.cropRect;

    // 计算相对于原始图片的裁剪坐标和尺寸
    const cropX = (rect.left! - obj.left!) / obj.scaleX!;
    const cropY = (rect.top! - obj.top!) / obj.scaleY!;
    const cropWidth = rect.width! / obj.scaleX!;
    const cropHeight = rect.height! / obj.scaleY!;
    const width = rect.width;
    const height = rect.height;

    // 设置裁剪参数
    // obj.set({
    //   cropX,
    //   cropY,
    //   cropWidth,
    //   cropHeight,
    //   width: cropWidth,
    //   height: cropHeight,
    //   left: rect.left,
    //   top: rect.top,
    // });

    // 移除裁剪矩形
    this.canvas.remove(this.store.cropRect);
    this.store.cropRect = null;
    this.store.cropObject = null;

    // 更新编辑器元素
    const element = this.store.editorElements.find(
      (el) => el.fabricObject === obj
    );
    if (element) {
      element.placement = {
        x: obj.left!,
        y: obj.top!,
        width: width,
        height: height,
        cropX: cropX,
        cropY: cropY,
        cropHeight: cropHeight,
        cropWidth: cropWidth,
        rotation: obj.angle!,
        scaleX: obj.scaleX!,
        scaleY: obj.scaleY!,
        flipX: obj.flipX,
        flipY: obj.flipY,
      };
      this.store.updateEditorElement(element, "元素属性修改");
    }
    this.refreshElements();
  }

  cancelCrop() {
    if (!this.store.cropRect || !this.canvas) return;
    this.canvas.remove(this.store.cropRect);
    this.store.cropRect = null;
    this.store.cropObject = null;
    this.store.canvas.renderAll();
  }

  refreshElements() {
    const store = this.store;
    if (!store.canvas) return;
    // 先根据轨道顺序更新元素数组
    store.updateCanvasOrderByTrackOrder();

    // 清除画布上的所有对象
    store.canvas.remove(...store.canvas.getObjects());

    // 确保所有元素的fabricObject为null，以便重新创建
    store.editorElements.forEach((element) => {
      element.fabricObject = null;
    });

    // 按照editorElements数组的顺序添加元素到画布
    // 注意：我们从后往前添加，这样第一个元素会显示在最上面
    for (let index = store.editorElements.length - 1; index >= 0; index--) {
      const element = store.editorElements[index];
      this.addElement(element);
    }

    store.refreshAnimations();
    store.updateTimeTo(store.currentTimeInMs);
    store.canvas.requestRenderAll();
  }

  setMediaFilter(
    id: string,
    filterType: "brightness" | "contrast" | "saturation" | "hue" | "blur",
    value: number
  ) {
    const element = this.store.editorElements.find((el) => el.id === id);
    if (
      !element ||
      !element.fabricObject ||
      !(
        element.fabricObject instanceof fabric.CoverImage ||
        element.fabricObject instanceof fabric.CoverVideo
      )
    )
      return;

    const mediaObject = element.fabricObject as
      | fabric.CoverImage
      | fabric.CoverVideo;

    switch (filterType) {
      case "brightness":
        (mediaObject as any).setBrightness(value);
        break;
      case "contrast":
        (mediaObject as any).setContrast(value);
        break;
      case "saturation":
        (mediaObject as any).setSaturation(value);
        break;
      case "hue":
        (mediaObject as any).setHue(value);
        break;
      case "blur":
        (mediaObject as any).setBlur(value);
        break;
    }

    if (element && isEditorMediaElement(element)) {
      console.log("update filters");
      const filters = {
        brightness: element.properties.filters.brightness ?? 0,
        contrast: element.properties.filters.contrast ?? 0,
        saturation: element.properties.filters.saturation ?? 0,
        vibrance: element.properties.filters.vibrance ?? 0,
        hue: element.properties.filters.hue ?? 0,
        noise: element.properties.filters.noise ?? 0,
        blur: element.properties.filters.blur ?? 0,
        ...element.properties.filters,
      };
      filters[filterType] = value;
      element.properties = {
        ...element.properties,
        filters,
      };
    }

    this.canvas.requestRenderAll();
  }

  setMediaBorder(id: string, border: Partial<BorderStyle>) {
    const element = this.store.editorElements.find((el) => el.id === id);
    if (
      !element ||
      !element.fabricObject ||
      !(element.fabricObject instanceof fabric.CoverImage)
    )
      return;

    const imageObject = element.fabricObject as fabric.CoverImage;
    // (imageObject as any).setBorder(border.color, border.width, border.style);

    if (element && isEditorImageElement(element)) {
      element.properties = {
        ...element.properties,
        border: {
          ...element.properties.border,
          ...border,
        },
      };
      // this.store.updateEditorElement({
      //   ...element,
      //   properties: {
      //     ...element.properties,
      //     border: {
      //       ...element.properties.border,
      //       ...border,
      //     },
      //   },
      // });
    }

    this.canvas.renderAll();
  }
  // ... existing code ...

  destroy() {
    // Clear any references to fabric objects in the store
    this.store.editorElements.forEach((element) => {
      if (element.fabricObject) {
        element.fabricObject = null;
      }
    });

    // Clear the elements array in the store
    this.store.setEditorElements([]);
    // Clear crop-related objects
    this.store.cropObject = null;
    this.store.cropRect = null;
  }

  refreshElement(element: EditorElement) {
    if (!this.canvas || !element.fabricObject) return;

    switch (element.type) {
      case "video": {
        const videoObject = element.fabricObject as fabric.CoverVideo;
        videoObject.set({
          left: element.placement.x,
          top: element.placement.y,
          width: element.placement.width,
          height: element.placement.height,
          cropHeight: element.placement.cropHeight,
          cropWidth: element.placement.cropWidth,
          scaleX: element.placement.scaleX,
          scaleY: element.placement.scaleY,
          angle: element.placement.rotation,
          flipX: element.placement.flipX || false,
          flipY: element.placement.flipY || false,
          opacity: element.opacity,
          // @ts-ignore
          locked: element.locked,
          selectable: !element.locked,
          brightness: element.properties.filters.brightness,
          contrast: element.properties.filters.contrast,
          saturation: element.properties.filters.saturation,
          hue: element.properties.filters.hue,
          blur: element.properties.filters.blur,
          // @ts-ignore
          customFilter: element.properties.effect.type,
        });
        break;
      }
      case "image": {
        const imageObject = element.fabricObject as fabric.CoverImage;
        imageObject.set({
          left: element.placement.x,
          top: element.placement.y,
          cropX: element.placement.cropX,
          cropY: element.placement.cropY,
          width: element.placement.width,
          height: element.placement.height,
          cropHeight: element.placement.cropHeight,
          cropWidth: element.placement.cropWidth,
          angle: element.placement.rotation,
          scaleX: element.placement.scaleX,
          scaleY: element.placement.scaleY,
          flipX: element.placement.flipX || false,
          flipY: element.placement.flipY || false,
          opacity: element.opacity,
          // @ts-ignore
          locked: element.locked,
          selectable: !element.locked,
          brightness: element.properties.filters.brightness,
          contrast: element.properties.filters.contrast,
          saturation: element.properties.filters.saturation,
          hue: element.properties.filters.hue,
          blur: element.properties.filters.blur,
          imageBorderColor: element.properties.border.color,
          borderWidth: element.properties.border.width,
          borderStyle: element.properties.border.style,
          borderRadius: element.properties.border.borderRadius,
          // @ts-ignore
          customFilter: element.properties.effect.type,
        });
        break;
      }
      case "text": {
        const textObject = element.fabricObject as fabric.TextboxWithPadding;
        textObject.set({
          left: element.placement.x,
          top: element.placement.y,
          width: element.placement.width,
          height: element.placement.height,
          scaleX: element.placement.scaleX,
          scaleY: element.placement.scaleY,
          angle: element.placement.rotation,
          opacity: element.opacity,
          text: element.properties.text,
          fontSize: element.properties.fontSize,
          fontFamily: element.properties.fontFamily || "sans-serif",
          textAlign: element.properties.textAlign || "left",
          lineHeight: element.properties.lineHeight || 1,
          charSpacing: element.properties.charSpacing || 10,
          fontWeight: element.properties.styles?.includes("bold")
            ? "bold"
            : "normal",
          fontStyle: element.properties.styles?.includes("italic")
            ? "italic"
            : "normal",
          underline: element.properties.styles?.includes("underlined"),
          linethrough: element.properties.styles?.includes("strikethrough"),
          strokeWidth: element.properties.strokeWidth || 0,
          stroke: element.properties.strokeColor || "#000000",
          backgroundColor: element.properties.backgroundColor || "transparent",
        });

        // Update text gradient if enabled
        if (
          element.properties.useGradient &&
          element.properties.gradientColors
        ) {
          const gradient = new fabric.Gradient({
            type: "linear",
            coords: { x1: 0, y1: 0, x2: textObject.width, y2: 0 },
            colorStops: [
              { offset: 0, color: element.properties.gradientColors[0] },
              { offset: 1, color: element.properties.gradientColors[1] },
            ],
          });
          textObject.set("fill", gradient);
        } else {
          textObject.set("fill", element.properties.fontColor || "#ffffff");
        }

        // Update shadow
        textObject.set(
          "shadow",
          new fabric.Shadow({
            color: element.properties.shadowColor || "#000000",
            blur: element.properties.shadowBlur || 0,
            offsetX: element.properties.shadowOffsetX || 0,
            offsetY: element.properties.shadowOffsetY || 0,
          })
        );
        break;
      }
      case "shape": {
        const shapeElement = element as ShapeEditorElement;
        const shapeObject = element.fabricObject;

        // 设置通用属性
        shapeObject.set({
          left: element.placement.x,
          top: element.placement.y,
          width: element.placement.width,
          height: element.placement.height,
          scaleX: element.placement.scaleX,
          scaleY: element.placement.scaleY,
          angle: element.placement.rotation,
          flipX: element.placement.flipX || false,
          flipY: element.placement.flipY || false,
          opacity: element.opacity,
          fill: shapeElement.properties.fill,
          stroke: shapeElement.properties.stroke,
          strokeWidth: shapeElement.properties.strokeWidth,
          selectable: !element.locked,
        });

        // 对于圆角矩形，更新圆角半径
        if (
          shapeElement.properties.shapeType === "roundedRect" &&
          shapeObject instanceof fabric.Rect
        ) {
          shapeObject.set({
            rx: shapeElement.properties.borderRadius || 10,
            ry: shapeElement.properties.borderRadius || 10,
          });
        }

        break;
      }
    }

    element.fabricObject.setCoords();
    this.canvas.requestRenderAll();
  }

  addElement(newElement: EditorElement) {
    const store = this.store;
    if (!store.canvas) return;
    const element = store.editorElements.find((el) => el.id === newElement.id);
    if (!element) return;

    // 确保媒体元素存在于DOM中
    if (
      (element.type === "video" ||
        element.type === "audio" ||
        element.type === "image") &&
      element.properties?.elementId
    ) {
      const mediaElementId = element.properties.elementId;
      const existingElement = document.getElementById(mediaElementId);

      if (!existingElement && element.properties.src) {
        console.log(
          `Creating missing media element: ${mediaElementId}, type: ${element.type}`
        );

        if (element.type === "video") {
          const videoElement = document.createElement("video");
          videoElement.id = mediaElementId;
          videoElement.src = element.properties.src;
          videoElement.crossOrigin = "anonymous";
          videoElement.style.display = "none";
          document.body.appendChild(videoElement);

          // 设置视频属性
          if ((element.properties as any).mediaStartTime) {
            videoElement.currentTime = (
              element.properties as any
            ).mediaStartTime;
          }
        } else if (element.type === "audio") {
          const audioElement = document.createElement("audio");
          audioElement.id = mediaElementId;
          audioElement.src = element.properties.src;
          audioElement.crossOrigin = "anonymous";
          audioElement.style.display = "none";
          document.body.appendChild(audioElement);

          // 设置音频属性
          if ((element.properties as any).mediaStartTime) {
            audioElement.currentTime = (
              element.properties as any
            ).mediaStartTime;
          }
        } else if (element.type === "image") {
          const imageElement = document.createElement("img");
          imageElement.id = mediaElementId;
          imageElement.src = element.properties.src;
          imageElement.crossOrigin = "anonymous";
          imageElement.style.display = "none";
          document.body.appendChild(imageElement);
        }
      }
    }

    switch (element.type) {
      case "video": {
        const videoElement = document.getElementById(
          element.properties.elementId
        );
        if (!isHtmlVideoElement(videoElement)) {
          console.error(
            `Video element not found or invalid: ${element.properties.elementId}`
          );
          return;
        }

        const videoObject = new CoverVideo(videoElement, {
          name: element.id,
          left: element.placement.x,
          locked: element.locked,
          opacity: element.opacity,
          top: element.placement.y,
          width: element.placement.width,
          height: element.placement.height,
          cropHeight: element.placement.cropHeight,
          cropWidth: element.placement.cropWidth,
          scaleX: element.placement.scaleX,
          scaleY: element.placement.scaleY,
          angle: element.placement.rotation,
          flipX: element.placement.flipX,
          flipY: element.placement.flipY,
          brightness: element.properties.filters.brightness,
          contrast: element.properties.filters.contrast,
          saturation: element.properties.filters.saturation,
          hue: element.properties.filters.hue,
          blur: element.properties.filters.blur,
          imageBorderColor: element.properties.border.color,
          borderWidth: element.properties.border.width,
          borderStyle: element.properties.border.style,
          borderRadius: element.properties.border.borderRadius,
          objectCaching: false,
          selectable: !element.locked,
          lockUniScaling: true,
          // filters: filters,
          // @ts-ignore
          customFilter: element.properties.effect.type,
        });

        element.fabricObject = videoObject;
        // element.properties.imageObject = videoObject;
        videoElement.width = 200;
        videoElement.height =
          (videoElement.videoHeight * 200) / videoElement.videoWidth;
        store.canvas.add(videoObject);

        store.canvas.on("object:modified", function (e) {
          console.log("object:modified");
          if (!e.target) return;
          const target = e.target;
          if (target != videoObject) return;
          const placement = element.placement;
          const newPlacement: Placement = {
            ...placement,
            x: target.left ?? placement.x,
            y: target.top ?? placement.y,
            rotation: target.angle ?? placement.rotation,
            width:
              target.width && target.scaleX
                ? target.width * target.scaleX
                : placement.width,
            height:
              target.height && target.scaleY
                ? target.height * target.scaleY
                : placement.height,
            // scaleX: target.scaleX ?? placement.scaleX,
            // scaleY: target.scaleY ?? placement.scaleY,
            // scaleX: 1,
            // scaleY: 1,
            // flipX: placement.flipX,
            // flipY: placement.flipY,
          };
          const newElement = {
            ...element, // 保留原始元素的所有属性
            placement: newPlacement, // 更新元素的位置信息
            properties: {
              ...element.properties, // 保留原始属性
              // filters: element.properties.filters || {}, // 如果filters不存在，���初始化为空对象
              // border: element.properties.border, // 保留边框属性
            },
          };
          store.updateEditorElement(newElement);
        });
        break;
      }

      case "image": {
        const imageElement = document.getElementById(
          element.properties.elementId
        );
        if (!isHtmlImageElement(imageElement)) return;
        const imageObject = new CoverImage(imageElement, {
          name: element.id,
          locked: element.locked,
          opacity: element.opacity,
          left: element.placement.x,
          top: element.placement.y,
          cropX: element.placement.cropX,
          cropY: element.placement.cropY,
          width: element.placement.width,
          height: element.placement.height,
          cropHeight: element.placement.cropHeight,
          cropWidth: element.placement.cropWidth,
          angle: element.placement.rotation,
          scaleX: element.placement.scaleX,
          scaleY: element.placement.scaleY,
          flipX: element.placement.flipX,
          flipY: element.placement.flipY,
          brightness: element.properties.filters.brightness,
          contrast: element.properties.filters.contrast,
          saturation: element.properties.filters.saturation,
          hue: element.properties.filters.hue,
          blur: element.properties.filters.blur,
          imageBorderColor: element.properties.border.color,
          borderWidth: element.properties.border.width,
          borderStyle: element.properties.border.style,
          borderRadius: element.properties.border.borderRadius,
          objectCaching: true,
          selectable: !element.locked,
          lockUniScaling: true,
          // filters
          // @ts-ignore
          customFilter: element.properties.effect.type,
        });
        // imageObject.applyFilters();
        element.fabricObject = imageObject;
        // element.properties.imageObject = imageObject;
        // 记录图片尺寸，用于调试
        const imageSize = {
          width: imageElement.naturalWidth,
          height: imageElement.naturalHeight,
        };
        console.log(`Image size: ${JSON.stringify(imageSize)}`);

        imageElement.width = 200;
        imageElement.height = (imageElement.height * 200) / imageElement.width;
        store.canvas.add(imageObject);
        store.canvas.on("object:modified", function (e) {
          if (!e.target) return;
          const target = e.target;
          if (target != imageObject) return;
          const placement = element.placement;
          const newPlacement: Placement = {
            ...placement,
            x: target.left ?? placement.x,
            y: target.top ?? placement.y,
            rotation: target.angle ?? placement.rotation,
            width:
              target.width && target.scaleX
                ? target.width * target.scaleX
                : placement.width,
            height:
              target.height && target.scaleY
                ? target.height * target.scaleY
                : placement.height,
            // scaleX: target.scaleX ?? placement.scaleX,
            // scaleY: target.scaleY ?? placement.scaleY,
            // scaleX: 1,
            // scaleY: 1,
            // flipX: placement.flipX,
            // flipY: placement.flipY,
          };
          // 创建一个新的元素对象，用于更新编辑器中的元素
          const newElement = {
            ...element, // 保留原始元素的所有属性
            placement: newPlacement, // 更新元素的位置信息
          };
          console.log("newElement", newElement);
          // 这个新元素对象包含了更新后的位置信息和属性，
          // 将被用于更新编辑器中的元素状态
          store.updateEditorElement(newElement);
        });
        break;
      }
      case "audio": {
        break;
      }
      case "text": {
        const textObject = new fabric.TextboxWithPadding(
          element.properties.text,
          {
            name: element.id,
            opacity: element.opacity,
            left: element.placement.x,
            top: element.placement.y,
            scaleX: element.placement.scaleX,
            scaleY: element.placement.scaleY,
            padding: 5,
            width: element.placement.width,
            height: element.placement.height,
            angle: element.placement.rotation,
            flipX: element.placement.flipX,
            flipY: element.placement.flipY,
            fontSize: element.properties.fontSize,
            fontFamily: element.properties.fontFamily || "sans-serif",
            textAlign: element.properties.textAlign || "left",
            lineHeight: element.properties.lineHeight || 1,
            charSpacing: element.properties.charSpacing || 0,
            fontWeight: element.properties.styles?.includes("bold")
              ? "bold"
              : "normal",
            fontStyle: element.properties.styles?.includes("italic")
              ? "italic"
              : "normal",
            underline: element.properties.styles?.includes("underlined"),
            linethrough: element.properties.styles?.includes("strikethrough"),
            objectCaching: false,
            selectable: true,
            lockUniScaling: true,

            strokeWidth: element.properties.strokeWidth || 0,
            stroke: element.properties.strokeColor || "#000000",
            shadow: new fabric.Shadow({
              color: element.properties.shadowColor || "#000000",
              blur: element.properties.shadowBlur || 0,
              offsetX: element.properties.shadowOffsetX || 0,
              offsetY: element.properties.shadowOffsetY || 0,
            }),
            backgroundColor:
              element.properties.backgroundColor || "transparent",
            //@ts-ignore
            rx: 10,
            //@ts-ignore
            ry: 10,
          }
        );

        // Add gradient support
        if (element.properties.useGradient) {
          const gradient = new fabric.Gradient({
            type: "linear",
            coords: { x1: 0, y1: 0, x2: textObject.width, y2: 0 },
            colorStops: [
              {
                offset: 0,
                color: element.properties.gradientColors[0],
              },
              {
                offset: 1,
                color: element.properties.gradientColors[1],
              },
            ],
          });

          textObject.set("fill", gradient);
        } else {
          textObject.set("fill", element.properties.fontColor || "#ffffff");
        }

        element.fabricObject = textObject;
        store.canvas.add(textObject);
        store.canvas.on("object:modified", function (e) {
          if (!e.target) return;
          const target = e.target;
          if (target != textObject) return;
          const placement = element.placement;
          const newPlacement: Placement = {
            ...placement,
            x: target.left ?? placement.x,
            y: target.top ?? placement.y,
            rotation: target.angle ?? placement.rotation,
            width: target.width ?? placement.width,
            height: target.height ?? placement.height,

            scaleX: target.scaleX ?? placement.scaleX,
            scaleY: target.scaleY ?? placement.scaleY,
            // flipX: placement.flipX,
            // flipY: placement.flipY,
          };
          const newElement = {
            ...element,
            placement: newPlacement,
            properties: {
              ...element.properties,
              // @ts-ignore
              text: target?.text,
            },
          };
          store.updateEditorElement(newElement);
        });
        break;
      }
      case "shape": {
        const shapeElement = element as ShapeEditorElement;
        const shapeType = shapeElement.properties.shapeType;
        let shapeObject: fabric.Object;

        const commonProps = {
          name: element.id,
          opacity: element.opacity,
          left: element.placement.x,
          top: element.placement.y,
          width: element.placement.width,
          height: element.placement.height,
          angle: element.placement.rotation,
          scaleX: element.placement.scaleX,
          scaleY: element.placement.scaleY,
          flipX: element.placement.flipX,
          flipY: element.placement.flipY,
          fill: shapeElement.properties.fill,
          stroke: shapeElement.properties.stroke,
          strokeWidth: shapeElement.properties.strokeWidth,
          objectCaching: true,
          selectable: !element.locked,
          lockUniScaling: true,
        };

        // 根据形状类型创建不同的对象
        switch (shapeType) {
          case "rect":
            shapeObject = new fabric.Rect(commonProps);
            break;
          case "roundedRect":
            shapeObject = new fabric.Rect({
              ...commonProps,
              rx: shapeElement.properties.borderRadius || 10,
              ry: shapeElement.properties.borderRadius || 10,
            });
            break;
          case "circle":
            // 对于圆形，使用椭圆并确保宽高相等
            shapeObject = new fabric.Circle({
              ...commonProps,
              radius: Math.min(commonProps.width, commonProps.height) / 2,
            });
            break;
          case "ellipse":
            shapeObject = new fabric.Ellipse({
              ...commonProps,
              rx: commonProps.width / 2,
              ry: commonProps.height / 2,
            });
            break;
          case "triangle":
            shapeObject = new fabric.Triangle(commonProps);
            break;
          case "line":
            shapeObject = new fabric.Line([0, 0, commonProps.width, 0], {
              ...commonProps,
              stroke: shapeElement.properties.stroke,
              strokeWidth: shapeElement.properties.strokeWidth || 5,
            });
            break;
          case "polygon":
          case "pentagon":
            // 创建五边形
            const pentagonPoints = this.calculatePolygonPoints(
              5,
              commonProps.width / 2
            );
            shapeObject = new fabric.Polygon(pentagonPoints, commonProps);
            break;
          case "hexagon":
            // 创建六边形
            const hexagonPoints = this.calculatePolygonPoints(
              6,
              commonProps.width / 2
            );
            shapeObject = new fabric.Polygon(hexagonPoints, commonProps);
            break;
          case "octagon":
            // 创建八边形
            const octagonPoints = this.calculatePolygonPoints(
              8,
              commonProps.width / 2
            );
            shapeObject = new fabric.Polygon(octagonPoints, commonProps);
            break;
          case "parallelogram":
            // 创建平行四边形
            const width = commonProps.width;
            const height = commonProps.height;
            const offset = width / 4;
            const parallelogramPoints = [
              { x: offset, y: 0 },
              { x: width, y: 0 },
              { x: width - offset, y: height },
              { x: 0, y: height },
            ];
            shapeObject = new fabric.Polygon(parallelogramPoints, commonProps);
            break;
          case "arch":
            // 创建拱形
            const archPath = [
              "M",
              0,
              commonProps.height,
              "L",
              0,
              commonProps.height / 2,
              "Q",
              commonProps.width / 2,
              0,
              commonProps.width,
              commonProps.height / 2,
              "L",
              commonProps.width,
              commonProps.height,
              "Z",
            ].join(" ");
            shapeObject = new fabric.Path(archPath, commonProps);
            break;
          default:
            // 默认创建矩形
            shapeObject = new fabric.Rect(commonProps);
        }

        element.fabricObject = shapeObject;
        store.canvas.add(shapeObject);
        store.canvas.on("object:modified", function (e) {
          if (!e.target) return;
          const target = e.target;
          if (target != shapeObject) return;
          const placement = element.placement;
          const newPlacement: Placement = {
            ...placement,
            x: target.left ?? placement.x,
            y: target.top ?? placement.y,
            rotation: target.angle ?? placement.rotation,
            width:
              target.width && target.scaleX
                ? target.width * target.scaleX
                : placement.width,
            height:
              target.height && target.scaleY
                ? target.height * target.scaleY
                : placement.height,
            scaleX: target.scaleX ?? placement.scaleX,
            scaleY: target.scaleY ?? placement.scaleY,
          };
          const newElement = {
            ...element,
            placement: newPlacement,
          };
          store.updateEditorElement(newElement);
        });
        break;
      }
      default: {
        throw new Error("Not implemented");
      }
    }
    if (element.fabricObject) {
      element.fabricObject.on("selected", function () {
        store.setSelectedElement(element);
      });
    }
  }
}
