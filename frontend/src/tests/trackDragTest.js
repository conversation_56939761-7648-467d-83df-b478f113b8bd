// 轨道拖拽测试脚本
// 这个脚本用于测试上下拖拽时间线元素后，Canvas上的元素显示顺序是否正确更新

/**
 * 测试步骤：
 * 1. 创建多个不同类型的轨道
 * 2. 在每个轨道上添加多个元素
 * 3. 模拟上下拖拽元素
 * 4. 验证Canvas上的元素显示顺序是否与轨道顺序一致
 */

function testTrackDrag(store) {
  console.log('开始测试上下拖拽时间线元素...');
  
  // 步骤1：获取当前所有轨道
  const tracks = store.trackManager.tracks;
  console.log('当前轨道数量:', tracks.length);
  
  if (tracks.length < 2) {
    console.warn('轨道数量不足，无法进行测试。请先创建至少两个轨道。');
    return false;
  }
  
  // 步骤2：获取第一个轨道上的元素
  const firstTrack = tracks[0];
  const firstTrackElements = store.trackManager.getElementsByTrackId(firstTrack.id);
  
  if (firstTrackElements.length === 0) {
    console.warn('第一个轨道上没有元素，无法进行测试。请先添加元素。');
    return false;
  }
  
  // 步骤3：获取第二个轨道
  const secondTrack = tracks[1];
  
  // 步骤4：记录拖拽前的Canvas元素顺序
  console.log('拖拽前的Canvas元素顺序:');
  const beforeElements = store.canvas.getObjects();
  beforeElements.forEach((obj, index) => {
    const element = store.editorElements.find(el => el.fabricObject === obj);
    if (element) {
      const track = store.trackManager.getTrackByElementId(element.id);
      console.log(`  ${index}: 元素ID ${element.id}, 轨道ID ${track ? track.id : 'none'}`);
    }
  });
  
  // 步骤5：模拟拖拽 - 将第一个轨道的第一个元素移动到第二个轨道
  const elementToMove = firstTrackElements[0];
  console.log(`将元素 ${elementToMove.id} 从轨道 ${firstTrack.id} 移动到轨道 ${secondTrack.id}`);
  
  // 执行移动
  store.trackManager.moveElementToTrack(elementToMove.id, secondTrack.id);
  
  // 步骤6：获取拖拽后的Canvas元素顺序
  console.log('拖拽后的Canvas元素顺序:');
  const afterElements = store.canvas.getObjects();
  afterElements.forEach((obj, index) => {
    const element = store.editorElements.find(el => el.fabricObject === obj);
    if (element) {
      const track = store.trackManager.getTrackByElementId(element.id);
      console.log(`  ${index}: 元素ID ${element.id}, 轨道ID ${track ? track.id : 'none'}`);
    }
  });
  
  // 步骤7：验证元素是否已经移动到第二个轨道
  const movedElement = store.trackManager.getElementsByTrackId(secondTrack.id)
    .find(el => el.id === elementToMove.id);
  
  if (!movedElement) {
    console.error('测试失败: 元素没有成功移动到第二个轨道');
    return false;
  }
  
  // 步骤8：验证Canvas上的元素显示顺序是否与轨道顺序一致
  const orderedElements = store.trackManager.getAllElementsInDisplayOrder();
  let isOrderCorrect = true;
  
  // 创建一个映射，将fabricObject映射到其在editorElements中的索引
  const elementIndexMap = new Map();
  store.editorElements.forEach((element, index) => {
    if (element.fabricObject) {
      elementIndexMap.set(element.fabricObject, index);
    }
  });
  
  // 检查Canvas上的元素顺序
  for (let i = 0; i < afterElements.length - 1; i++) {
    const currentElement = afterElements[i];
    const nextElement = afterElements[i + 1];
    
    const currentIndex = elementIndexMap.get(currentElement);
    const nextIndex = elementIndexMap.get(nextElement);
    
    if (currentIndex !== undefined && nextIndex !== undefined) {
      // Canvas上的元素顺序应该与editorElements中的顺序相反
      // 因为Canvas是从后往前渲染的
      if (currentIndex < nextIndex) {
        console.error(`元素顺序错误: Canvas索引 ${i} (editorElements索引 ${currentIndex}) 应该在 Canvas索引 ${i + 1} (editorElements索引 ${nextIndex}) 之后`);
        isOrderCorrect = false;
      }
    }
  }
  
  if (isOrderCorrect) {
    console.log('测试通过: 上下拖拽后Canvas上的元素显示顺序与轨道顺序一致');
  } else {
    console.error('测试失败: 上下拖拽后Canvas上的元素显示顺序与轨道顺序不一致');
  }
  
  return isOrderCorrect;
}

// 导出测试函数
export default testTrackDrag;
