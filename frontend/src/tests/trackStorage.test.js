// 轨道存储测试脚本
// 这个脚本用于测试轨道和轨道元素信息是否能够正确保存和恢复

// 模拟localStorage
const mockLocalStorage = (() => {
  let store = {};
  return {
    getItem: (key) => store[key] || null,
    setItem: (key, value) => {
      store[key] = value.toString();
    },
    clear: () => {
      store = {};
    },
    removeItem: (key) => {
      delete store[key];
    },
    getStore: () => store,
  };
})();

// 模拟Store类
class MockStore {
  constructor() {
    this.autoSaveKey = "fabric-canvas-state";
    this.editorElements = [];
    this.trackManager = {
      tracks: [],
      defaultTracks: {
        media: "track-1",
        audio: "track-2",
        text: "track-3",
        caption: "track-4",
      },
    };
  }

  // 模拟exportCanvasState方法
  exportCanvasState() {
    const canvasState = {
      backgroundColor: "#111111",
      width: 1280,
      height: 720,
      elements: this.editorElements.map(({ fabricObject, ...element }) => ({
        ...element,
        timeFrame: {
          start: element.timeFrame.start,
          end: element.timeFrame.end,
        },
      })),
      tracks: this.trackManager.tracks,
      defaultTracks: this.trackManager.defaultTracks,
    };
    return JSON.stringify(canvasState);
  }

  // 模拟saveToLocalStorage方法
  saveToLocalStorage() {
    try {
      const state = this.exportCanvasState();
      mockLocalStorage.setItem(this.autoSaveKey, state);
      console.log("保存到localStorage成功");
    } catch (error) {
      console.error("保存到localStorage失败:", error);
    }
  }

  // 模拟loadFromLocalStorage方法
  loadFromLocalStorage() {
    const savedState = mockLocalStorage.getItem(this.autoSaveKey);
    if (savedState) {
      console.log("从localStorage加载成功");
      this.importCanvasState(savedState);
      return true;
    }
    console.log("localStorage中没有保存的状态");
    return false;
  }

  // 模拟importCanvasState方法
  importCanvasState(jsonState) {
    try {
      const canvasData = JSON.parse(jsonState);
      
      // 导入元素
      this.editorElements = canvasData.elements;
      
      // 导入轨道信息
      if (canvasData.tracks) {
        this.trackManager.tracks = canvasData.tracks;
      }
      
      // 导入默认轨道信息
      if (canvasData.defaultTracks) {
        this.trackManager.defaultTracks = canvasData.defaultTracks;
      }
      
      console.log("导入状态成功");
      return true;
    } catch (error) {
      console.error("导入状态失败:", error);
      return false;
    }
  }
}

// 测试函数
function testTrackStorage() {
  console.log("开始测试轨道存储功能");
  
  // 创建Store实例
  const store = new MockStore();
  
  // 创建测试数据
  store.trackManager.tracks = [
    {
      id: "track-1",
      name: "Media Track 1",
      type: "media",
      elementIds: ["element-1", "element-2"],
      isVisible: true,
      isLocked: false,
    },
    {
      id: "track-2",
      name: "Audio Track 1",
      type: "audio",
      elementIds: ["element-3"],
      isVisible: true,
      isLocked: false,
    },
    {
      id: "track-3",
      name: "Text Track 1",
      type: "text",
      elementIds: ["element-4"],
      isVisible: true,
      isLocked: false,
    },
  ];
  
  store.editorElements = [
    {
      id: "element-1",
      name: "Video 1",
      type: "video",
      timeFrame: { start: 0, end: 5000 },
      trackId: "track-1",
    },
    {
      id: "element-2",
      name: "Image 1",
      type: "image",
      timeFrame: { start: 5000, end: 10000 },
      trackId: "track-1",
    },
    {
      id: "element-3",
      name: "Audio 1",
      type: "audio",
      timeFrame: { start: 0, end: 8000 },
      trackId: "track-2",
    },
    {
      id: "element-4",
      name: "Text 1",
      type: "text",
      timeFrame: { start: 2000, end: 7000 },
      trackId: "track-3",
    },
  ];
  
  // 保存到localStorage
  store.saveToLocalStorage();
  
  // 验证保存的数据
  const savedState = mockLocalStorage.getItem(store.autoSaveKey);
  const parsedState = JSON.parse(savedState);
  
  console.log("保存的轨道数量:", parsedState.tracks.length);
  console.log("保存的元素数量:", parsedState.elements.length);
  
  // 创建新的Store实例
  const newStore = new MockStore();
  
  // 从localStorage加载
  newStore.loadFromLocalStorage();
  
  // 验证加载的数据
  console.log("加载后的轨道数量:", newStore.trackManager.tracks.length);
  console.log("加载后的元素数量:", newStore.editorElements.length);
  
  // 验证轨道和元素的关联关系
  newStore.trackManager.tracks.forEach(track => {
    console.log(`轨道 ${track.name} 包含 ${track.elementIds.length} 个元素`);
    track.elementIds.forEach(elementId => {
      const element = newStore.editorElements.find(el => el.id === elementId);
      if (element) {
        console.log(`  - 元素 ${element.name} 的trackId: ${element.trackId}`);
        if (element.trackId !== track.id) {
          console.error(`  - 错误: 元素的trackId与轨道ID不匹配!`);
        }
      } else {
        console.error(`  - 错误: 找不到ID为 ${elementId} 的元素!`);
      }
    });
  });
  
  console.log("测试完成");
}

// 运行测试
testTrackStorage();
