import React from "react";
import { CssBaseline } from "@mui/material";
import { StoreProvider as ElementProvideo } from "./store";
import { StoreProvider } from "./store/store-context";
import Editor from "./editor/Editor";
import { CustomThemeProvider } from "./theme/ThemeContext";
import LoadingOverlay from "./components/LoadingOverlay";

const App: React.FC = () => {
  return (
    <CustomThemeProvider>
      <CssBaseline />
      <StoreProvider>
        <ElementProvideo>
          <LoadingOverlay />
          <Editor />
        </ElementProvideo>
      </StoreProvider>
    </CustomThemeProvider>
  );
};

export default App;
