import { useEffect } from "react";

export const useCanvasDrawing = (
  canvasRef: React.RefObject<HTMLCanvasElement>,
  drawFunction: (
    ctx: CanvasRenderingContext2D,
    width: number,
    height: number
  ) => void,
  dependencies: any[]
) => {
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const dpr = window.devicePixelRatio || 1;
    const { width, height } = canvas.getBoundingClientRect();

    canvas.width = width * dpr;
    canvas.height = height * dpr;
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;

    ctx.scale(dpr, dpr);
    ctx.imageSmoothingEnabled = false;
    ctx.translate(0.5, 0.5);

    drawFunction(ctx, width, height);
  }, dependencies);
};
