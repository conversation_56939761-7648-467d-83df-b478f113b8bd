import { CanvasState } from "../../types";
import { AbstractFilterGenerator } from "./BaseFilter";

/**
 * Interface for text element properties
 */
interface TextProperties {
  text?: string;
  fontFamily?: string;
  fontSize?: number;
  fontColor?: string;
  fontWeight?: string;
  fontStyle?: string;
  textAlign?: string;
  styles?: string[];
  backgroundColor?: string;
  backgroundPaddingX?: number;
  backgroundPaddingY?: number;
  strokeWidth?: number;
  strokeColor?: string;
  shadowBlur?: number;
  shadowOffsetX?: number;
  shadowOffsetY?: number;
  shadowColor?: string;
}

/**
 * Filter generator for text elements
 * Handles text-specific operations like font styling, alignment, and effects
 */
export class TextFilterGenerator extends AbstractFilterGenerator {
  /**
   * Directory containing font files
   */
  private fontDir: string = process.env.FONT_DIR || "./assets/fonts";

  /**
   * Generates an FFmpeg filter string for text elements
   *
   * @param inputIndex Index of the input stream or -1 for original
   * @param element Canvas element to generate filter for
   * @param startTime Start time in milliseconds
   * @param duration Duration in seconds
   * @param index Element index for labeling
   * @returns FFmpeg filter string for the text element
   */
  generateFilter(
    inputIndex: number,
    element: CanvasState["elements"][0],
    startTime: number,
    duration: number,
    index: number
  ): string {
    const { properties, placement } = element;
    const textProps = properties as TextProperties;

    if (!textProps.text || !placement) {
      throw new Error("Text element missing required properties");
    }

    // Escape special characters in text
    const text = this.escapeText(textProps.text);

    // Determine font path based on font family and styles
    const fontPath = this.determineFontPath(textProps);

    // Extract and normalize text styling properties
    const fontSize = Math.round(
      (textProps.fontSize || 16) * (placement.scaleX || 1)
    );
    const fontColor = textProps.fontColor || "#ffffff";
    const colorHex = fontColor.replace("#", "");
    const opacity = element.opacity ?? 1;
    const textAlign = textProps.textAlign || "left";

    // Background properties
    const hasBackground = Boolean(
      textProps.backgroundColor && textProps.backgroundColor !== ""
    );
    const bgColorHex =
      hasBackground && textProps.backgroundColor
        ? textProps.backgroundColor.replace("#", "")
        : "000000";
    const paddingX = textProps.backgroundPaddingX || 10;
    const paddingY = textProps.backgroundPaddingY || 5;

    // Stroke (border) properties
    const hasStroke = textProps.strokeWidth && textProps.strokeWidth > 0;
    const strokeWidth = hasStroke ? textProps.strokeWidth : 0;
    const strokeColorHex = textProps.strokeColor
      ? textProps.strokeColor.replace("#", "")
      : "000000";

    // Shadow properties
    const hasShadow = textProps.shadowBlur && textProps.shadowBlur > 0;
    const shadowX = textProps.shadowOffsetX || 0;
    const shadowY = textProps.shadowOffsetY || 0;
    const shadowBlur = textProps.shadowBlur || 0;
    const shadowColorHex = textProps.shadowColor
      ? textProps.shadowColor.replace("#", "")
      : "000000";

    // Build the base filter string
    let filterString =
      "drawtext=" +
      `text='${text}':` +
      `fontfile='${fontPath}':` +
      `fontsize=${fontSize}:` +
      `fontcolor=0x${colorHex}@${opacity}:` +
      `x=${placement.x}:` +
      `y=${placement.y}`;

    // Add background if specified
    if (hasBackground) {
      filterString = this.applyBackground(
        filterString,
        placement.y,
        bgColorHex,
        opacity,
        fontSize
      );
    }

    // Add border/stroke if specified
    if (hasStroke) {
      filterString += `:borderw=${strokeWidth}:bordercolor=0x${strokeColorHex}@${opacity}`;
    }

    // Add shadow if specified
    if (hasShadow) {
      filterString = this.applyShadow(
        filterString,
        shadowX,
        shadowY,
        shadowColorHex,
        shadowBlur,
        opacity
      );
    }

    // Apply text alignment
    filterString = this.applyTextAlignment(
      filterString,
      textAlign,
      placement.x
    );

    // Apply rotation if specified
    if (placement.rotation && placement.rotation !== 0) {
      filterString += `:box=1:boxcolor=0x00000000`;
      filterString += `:angle=${placement.rotation}`;
    }

    return filterString;
  }

  /**
   * Escapes special characters in text for FFmpeg
   *
   * @param text Raw text input
   * @returns Escaped text safe for FFmpeg filters
   */
  private escapeText(text: string): string {
    return text
      .replace(/'/g, "'\\''")
      .replace(/[\[\]]/g, "\\$&")
      .replace(/:/g, "\\:")
      .replace(/\n/g, "\\n");
  }

  /**
   * Determines the font file path based on font family and styles
   *
   * @param props Text properties
   * @returns Path to the font file
   */
  private determineFontPath(props: TextProperties): string {
    const baseFontFamily = props.fontFamily || "Arial";

    if (props.styles?.includes("bold") && props.styles?.includes("italic")) {
      return `${this.fontDir}/${baseFontFamily} Bold Italic.ttf`;
    } else if (props.styles?.includes("bold")) {
      return `${this.fontDir}/${baseFontFamily} Bold.ttf`;
    } else if (props.styles?.includes("italic")) {
      return `${this.fontDir}/${baseFontFamily} Italic.ttf`;
    }

    return `${this.fontDir}/${baseFontFamily}.ttf`;
  }

  /**
   * Applies background to text
   *
   * @param filterString Current filter string
   * @param y Y position
   * @param bgColorHex Background color in hex
   * @param opacity Opacity value
   * @param fontSize Font size
   * @returns Updated filter string with background
   */
  private applyBackground(
    filterString: string,
    y: number,
    bgColorHex: string,
    opacity: number,
    fontSize: number
  ): string {
    const padding = 40; // Unified padding value

    // Adjust y position to account for padding
    filterString = filterString.replace(
      `y=${y}`,
      `y=${y}+${padding}` // Offset by padding distance
    );

    // Add box properties
    filterString +=
      `:box=1:boxcolor=0x${bgColorHex}@${opacity}:` +
      `boxborderw=${padding}:` +
      `boxh=${fontSize}`;

    return filterString;
  }

  /**
   * Applies shadow effect to text
   *
   * @param filterString Current filter string
   * @param shadowX X offset of shadow
   * @param shadowY Y offset of shadow
   * @param shadowColorHex Shadow color in hex
   * @param shadowBlur Blur amount
   * @param opacity Base opacity
   * @returns Updated filter string with shadow
   */
  private applyShadow(
    filterString: string,
    shadowX: number,
    shadowY: number,
    shadowColorHex: string,
    shadowBlur: number,
    opacity: number
  ): string {
    filterString += `:shadowx=${shadowX}:shadowy=${shadowY}:shadowcolor=0x${shadowColorHex}@${opacity}`;

    // Approximate shadow blur by adjusting opacity
    if (shadowBlur > 0) {
      const blurOpacity = Math.max(
        0,
        Math.min(1, opacity * (1 - shadowBlur / 10))
      );
      filterString = filterString.replace(
        `shadowcolor=0x${shadowColorHex}@${opacity}`,
        `shadowcolor=0x${shadowColorHex}@${blurOpacity}`
      );
    }

    return filterString;
  }

  /**
   * Applies text alignment
   *
   * @param filterString Current filter string
   * @param textAlign Alignment type (left, center, right)
   * @param x X position
   * @returns Updated filter string with alignment
   */
  private applyTextAlignment(
    filterString: string,
    textAlign: string,
    x: number
  ): string {
    if (textAlign === "center") {
      return filterString.replace(`x=${x}`, `x=(w-tw)/2+${x}`);
    } else if (textAlign === "right") {
      return filterString.replace(`x=${x}`, `x=w-tw-${x}`);
    }

    return filterString;
  }
}
