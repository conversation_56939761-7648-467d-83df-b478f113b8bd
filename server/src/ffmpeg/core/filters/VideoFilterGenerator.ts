import { CanvasState } from "../../types";
import { AbstractFilterGenerator } from "./BaseFilter";

export class VideoFilterGenerator extends AbstractFilterGenerator {
  generateFilter(
    inputIndex: number,
    element: CanvasState["elements"][0],
    duration: number,
    index: number
  ): string {
    const { placement, properties, timeFrame } = element;
    const { filters, effect } = properties;

    // 获取mediaStartTime属性，如果不存在则默认为0
    const mediaStartTime = properties.mediaStartTime
      ? Number(properties.mediaStartTime)
      : 0;

    if (!placement) {
      throw new Error(`Video element at index ${index} missing placement data`);
    }
    console.log("video duration", duration);
    console.log("media start time", mediaStartTime);

    // 修改初始过滤器字符串，加入mediaStartTime
    let filterString =
      this.getInitialFilterString(inputIndex, index) +
      // 从mediaStartTime开始裁剪视频，持续duration秒
      `trim=start=${mediaStartTime.toFixed(3)}:duration=${duration.toFixed(
        3
      )},` +
      `setpts=PTS-STARTPTS+${(timeFrame.start / 1000).toFixed(3)}/TB,`;

    // Apply playback speed if specified
    if (element.playbackSpeed !== undefined) {
      filterString = this.applyPlaybackSpeed(
        filterString,
        element.playbackSpeed
      );
    }

    // Scale and crop
    const targetWidth = Math.round(placement.width);
    const targetHeight = Math.round(placement.height);
    filterString = this.applyScaleAndCrop(
      filterString,
      targetWidth,
      targetHeight
    );
    // Handle rotation
    filterString = this.applyRotation(
      filterString,
      placement.rotation || 0,
      targetWidth,
      targetHeight
    );

    // Set format
    filterString = this.applyFormat(filterString);

    // Apply flips
    filterString = this.applyFlips(
      filterString,
      placement.flipX,
      placement.flipY
    );

    // Apply opacity
    filterString = this.applyOpacity(filterString, element.opacity);

    // Apply volume if specified
    if (element.volume !== undefined) {
      filterString = this.applyVolume(filterString, element.volume);
    }

    // Apply common filters
    filterString = this.applyEffects(filterString, effect as any);
    filterString = this.applyFilters(filterString, filters);
    filterString = this.applyBorder(filterString, properties.border as any);

    return filterString + `[vid${index}]`;
  }

  /**
   * Applies playback speed adjustment to video
   * @param filterString Current filter string
   * @param playbackSpeed Speed multiplier (0.5-2.0 recommended)
   * @returns Updated filter string with speed adjustment applied
   */
  private applyPlaybackSpeed(
    filterString: string,
    playbackSpeed: number
  ): string {
    if (playbackSpeed === 1.0) return filterString;

    // For video, we need to adjust both PTS and the duration
    // When speeding up (playbackSpeed > 1), we need to extend the input duration
    // to ensure we have enough frames for the entire output duration
    const ptsMultiplier = 1.0 / playbackSpeed;

    // First, add the setpts filter to adjust the playback speed
    // 更新正则表达式以匹配新的setpts格式
    const setptsRegex = /setpts=PTS-STARTPTS\+([^\/]+)\/TB/;
    const match = filterString.match(setptsRegex);

    if (match) {
      const timeOffset = match[1];
      // Replace with speed-adjusted setpts
      filterString = filterString.replace(
        setptsRegex,
        `setpts=${ptsMultiplier}*(PTS-STARTPTS+${timeOffset}/TB)`
      );
    } else {
      // If no existing setpts found (shouldn't happen), append the speed filter
      filterString = filterString + `,setpts=${ptsMultiplier}*PTS`;
    }

    // For speeds > 1, we need to ensure we have enough input duration
    // Find the trim filter and adjust its duration if present
    if (playbackSpeed > 1) {
      // 更新正则表达式以匹配新的trim格式
      const trimRegex = /trim=start=([0-9.]+):duration=([0-9.]+)/;
      const trimMatch = filterString.match(trimRegex);

      if (trimMatch) {
        const startTime = parseFloat(trimMatch[1]);
        const originalDuration = parseFloat(trimMatch[2]);
        // Extend the trim duration by the playback speed factor
        // This ensures we have enough frames for the entire output duration
        const adjustedDuration = originalDuration * playbackSpeed;
        filterString = filterString.replace(
          trimRegex,
          `trim=start=${startTime.toFixed(
            3
          )}:duration=${adjustedDuration.toFixed(3)}`
        );
      }
    }

    return filterString;
  }

  /**
   * Applies volume adjustment to video's audio track
   * @param filterString Current filter string
   * @param volume Volume level (0.0-1.0 recommended)
   * @returns Updated filter string with volume adjustment applied
   */
  private applyVolume(filterString: string, volume: number): string {
    // Note: This doesn't directly affect the video filter chain
    // The actual volume adjustment happens in the audio processing
    // This is a placeholder in case we need to mark videos that need volume adjustment
    return filterString;
  }
}
