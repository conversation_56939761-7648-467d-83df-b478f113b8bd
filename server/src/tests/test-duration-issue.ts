import { FFmpegCommandGenerator } from "../ffmpeg/FFmpegCommandGenerator";
import { CanvasState } from "../ffmpeg/types";

/**
 * 测试用户提供的具体案例，分析视频时长计算问题
 */
async function testDurationIssue() {
  console.log("开始分析用户提供的视频时长问题...");

  // 用户提供的输入数据
  const canvasState: CanvasState = {
    backgroundColor: "#66BB6A",
    width: 1280,
    height: 640,
    elements: [
      {
        id: "4jekvpo",
        opacity: 1,
        locked: false,
        name: "Media(image) 1",
        type: "image",
        placement: {
          x: 424.99,
          y: 170,
          width: 430.01,
          height: 300,
          rotation: 0,
          scaleX: 1,
          scaleY: 1,
          flipX: false,
          flipY: false,
        },
        timeFrame: {
          start: 16488,
          end: 25035, // 25.035秒
        },
        properties: {
          elementId: "image-4jekvpo",
          src: "https://pixabay.com/get/gbe534670d37d07e4c0315de6f3dd57c692f1d2003452ad9e630fa92f48d37dfc6618e8f9dec2cbf15f28d193ef5abdebc8b89f051e198bc86eaec496f73f5e85_1280.jpg",
          effect: { type: "none" },
          filters: { type: "none" },
          border: {
            width: 0,
            color: "black",
            style: "solid",
            borderRadius: 0,
          },
        },
        trackId: "8ce4xkl",
      },
      {
        id: "jg23pvi",
        locked: false,
        opacity: 1,
        name: "Media(video) 1",
        type: "video",
        placement: {
          x: 524.62,
          y: 120,
          width: 230.77,
          height: 400,
          rotation: 0,
          scaleX: 1,
          scaleY: 1,
          flipX: false,
          flipY: false,
        },
        timeFrame: {
          start: 4084,
          end: 12637, // 12.637秒
        },
        properties: {
          elementId: "video-jg23pvi",
          src: "https://videos.pexels.com/video-files/7438482/7438482-sd_360_624_30fps.mp4",
          originalDuration: 8553.33,
          effect: { type: "none" },
          filters: { type: "none" },
          border: {
            width: 0,
            color: "black",
            style: "solid",
            borderRadius: 0,
          },
          mediaStartTime: 0,
        },
        trackId: "8ce4xkl",
      },
    ],
    animations: [],
    captions: [],
    globalCaptionStyle: {
      fontSize: 35,
      fontFamily: "Arial",
      fontColor: "#FFFFFF",
      fontWeight: 700,
      textAlign: "center",
      lineHeight: 1.2,
      charSpacing: 0,
      styles: ["bold", "italic", "underline"],
      strokeWidth: 1.3,
      strokeColor: "#d79329",
      shadowColor: "#000000",
      shadowBlur: 2,
      shadowOffsetX: 1,
      shadowOffsetY: 1,
      backgroundColor: "transparent",
      useGradient: false,
      gradientColors: ["#FFFFFF", "#000000"],
    },
    tracks: [
      {
        id: "8ce4xkl",
        name: "Media Track 2",
        type: "media",
        elementIds: ["jg23pvi", "4jekvpo"],
        isVisible: true,
        isLocked: false,
      },
    ],
  };

  console.log("分析输入数据:");
  console.log("元素1 (图片): start=16488ms (16.488s), end=25035ms (25.035s)");
  console.log("元素2 (视频): start=4084ms (4.084s), end=12637ms (12.637s)");
  console.log("字幕数量:", canvasState.captions?.length || 0);

  // 计算预期的最大结束时间
  const maxEndTime =
    Math.max(...canvasState.elements.map((el) => el.timeFrame.end)) / 1000;
  console.log(`预期的最大结束时间: ${maxEndTime}秒`);

  // 创建命令生成器
  const commandGenerator = new FFmpegCommandGenerator();

  try {
    // 生成FFmpeg命令
    const command = await commandGenerator.generateCommand(canvasState);

    console.log("\n生成的FFmpeg命令:");
    console.log(command);

    // 分析命令中的各种时长
    console.log("\n=== 时长分析 ===");

    // 1. 背景色时长
    const bgDurationMatch = command.match(
      /-f lavfi -i color=c=#66BB6A:s=1280x640:d=([0-9.]+)/
    );
    if (bgDurationMatch) {
      const bgDuration = parseFloat(bgDurationMatch[1]);
      console.log(`背景色时长: ${bgDuration}秒`);
    }

    // 2. 视频trim时长
    const videoTrimMatch = command.match(
      /\[1:v\]trim=start=0\.000:duration=([0-9.]+)/
    );
    if (videoTrimMatch) {
      const videoTrimDuration = parseFloat(videoTrimMatch[1]);
      console.log(`视频trim时长: ${videoTrimDuration}秒`);
    }

    // 3. 各种背景时长
    const bgMatches = command.match(/color=c=black@0:s=1280x640:d=([0-9.]+)/g);
    if (bgMatches) {
      console.log("发现的背景时长:");
      bgMatches.forEach((match, index) => {
        const duration = match.match(/d=([0-9.]+)/)?.[1];
        console.log(`  背景${index + 1}: ${duration}秒`);
      });
    }

    // 4. 音频时长
    const audioTrimMatch = command.match(
      /\[1:a\]atrim=start=0\.000:duration=([0-9.]+)/
    );
    if (audioTrimMatch) {
      const audioTrimDuration = parseFloat(audioTrimMatch[1]);
      console.log(`音频trim时长: ${audioTrimDuration}秒`);
    }

    console.log("\n=== 问题分析 ===");
    if (bgDurationMatch) {
      const bgDuration = parseFloat(bgDurationMatch[1]);
      if (bgDuration > maxEndTime) {
        console.log(
          `❌ 问题确认: 背景时长(${bgDuration}s) > 最大元素结束时间(${maxEndTime}s)`
        );
        console.log(`差异: ${(bgDuration - maxEndTime).toFixed(3)}秒`);
      } else {
        console.log(`✅ 背景时长正确: ${bgDuration}秒 = 最大元素结束时间`);
      }
    }
  } catch (error) {
    console.error("测试过程中发生错误:", error);
  } finally {
    // 清理资源
    commandGenerator.destroy();
  }
}

// 运行测试
testDurationIssue().catch(console.error);
