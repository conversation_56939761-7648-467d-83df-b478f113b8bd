{"name": "online-video-server", "version": "1.0.0", "description": "Server for online video editor", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn src/index.ts", "build": "tsc", "lint": "eslint . --ext .ts", "test": "jest"}, "dependencies": {"@aws-sdk/client-transcribe": "^3.782.0", "axios": "^1.8.4", "cors": "^2.8.5", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^8.1.0", "winston": "^3.17.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/node": "^22.14.0", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "eslint": "^9.24.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}